.vscode
# whisperkit
whisperkit
.DS_Store
/.build
/Packages
xcuserdata/
DerivedData/
.swiftpm/configuration/registries.json
.swiftpm/xcode/package.xcworkspace/contents.xcworkspacedata
.swiftpm/xcode/xcshareddata/
**/*.xcscheme
.netrc
.env

# Core ML Model Files
Models
**/*.mlpackage
**/*.mlmodel
**/*.mlmodelc
**/*.zip
**/*.tar.gz

# Audio files (add manually if needed)
**/*.wav
**/*.mp3
**/*.m4a
**/*.flac

## Xcode
# Build generated
build/
DerivedData/

# Various settings
*.pbxuser
!default.pbxuser
*.mode1v3
!default.mode1v3
*.mode2v3
!default.mode2v3
*.perspectivev3
!default.perspectivev3
xcuserdata/

# Other
*.moved-aside
*.xccheckout
*.xcscmblueprint

# Obj-C/Swift specific
*.hmap
*.ipa
*.dSYM.zip
*.dSYM

# fastlane
fastlane/report.xml
fastlane/Preview.html
fastlane/screenshots
fastlane/test_output

### Xcode Patch ###
*.xcodeproj/*
!*.xcodeproj/project.pbxproj
!*.xcodeproj/xcshareddata/
!*.xcworkspace/contents.xcworkspacedata
/*.gcno

*.json
*.srt
Info.plist
.vscode
whisperkit

readme.md
config.swift