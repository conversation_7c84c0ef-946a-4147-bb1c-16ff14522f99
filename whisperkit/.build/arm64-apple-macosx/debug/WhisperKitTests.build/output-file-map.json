{"": {"swift-dependencies": "/Users/<USER>/VideoText/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/master.swiftdeps"}, "/Users/<USER>/VideoText/whisperkit/Tests/WhisperKitTests/FunctionalTests.swift": {"dependencies": "/Users/<USER>/VideoText/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/FunctionalTests.d", "object": "/Users/<USER>/VideoText/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/FunctionalTests.swift.o", "swiftmodule": "/Users/<USER>/VideoText/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/FunctionalTests~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/VideoText/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/FunctionalTests.swiftdeps"}, "/Users/<USER>/VideoText/whisperkit/Tests/WhisperKitTests/MemoryTestUtils.swift": {"dependencies": "/Users/<USER>/VideoText/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/MemoryTestUtils.d", "object": "/Users/<USER>/VideoText/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/MemoryTestUtils.swift.o", "swiftmodule": "/Users/<USER>/VideoText/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/MemoryTestUtils~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/VideoText/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/MemoryTestUtils.swiftdeps"}, "/Users/<USER>/VideoText/whisperkit/Tests/WhisperKitTests/RegressionTests.swift": {"dependencies": "/Users/<USER>/VideoText/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/RegressionTests.d", "object": "/Users/<USER>/VideoText/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/RegressionTests.swift.o", "swiftmodule": "/Users/<USER>/VideoText/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/RegressionTests~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/VideoText/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/RegressionTests.swiftdeps"}, "/Users/<USER>/VideoText/whisperkit/Tests/WhisperKitTests/TestUtils.swift": {"dependencies": "/Users/<USER>/VideoText/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/TestUtils.d", "object": "/Users/<USER>/VideoText/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/TestUtils.swift.o", "swiftmodule": "/Users/<USER>/VideoText/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/TestUtils~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/VideoText/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/TestUtils.swiftdeps"}, "/Users/<USER>/VideoText/whisperkit/Tests/WhisperKitTests/UnitTests.swift": {"dependencies": "/Users/<USER>/VideoText/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/UnitTests.d", "object": "/Users/<USER>/VideoText/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/UnitTests.swift.o", "swiftmodule": "/Users/<USER>/VideoText/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/UnitTests~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/VideoText/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/UnitTests.swiftdeps"}, "/Users/<USER>/VideoText/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/DerivedSources/resource_bundle_accessor.swift": {"dependencies": "/Users/<USER>/VideoText/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/resource_bundle_accessor.d", "object": "/Users/<USER>/VideoText/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/resource_bundle_accessor.swift.o", "swiftmodule": "/Users/<USER>/VideoText/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/resource_bundle_accessor~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/VideoText/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/resource_bundle_accessor.swiftdeps"}}