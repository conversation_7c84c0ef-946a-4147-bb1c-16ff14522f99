// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 70;
	objects = {

/* Begin PBXBuildFile section */
		AF5E68682C9BD7E8008DBC6A /* VideoTextDetectorApp.swift in Sources */ = {isa = PBXBuildFile; fileRef = AF5E68672C9BD7E8008DBC6A /* VideoTextDetectorApp.swift */; };
		AF5E686C2C9BD7E9008DBC6A /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = AF5E686B2C9BD7E9008DBC6A /* Assets.xcassets */; };
		AF5E686F2C9BD7E9008DBC6A /* Preview Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = AF5E686E2C9BD7E9008DBC6A /* Preview Assets.xcassets */; };
		AF5E687A2C9BD7E9008DBC6A /* VideoTextDetectorTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = AF5E68792C9BD7E9008DBC6A /* VideoTextDetectorTests.swift */; };
		AF5E68842C9BD7E9008DBC6A /* VideoTextDetectorUITests.swift in Sources */ = {isa = PBXBuildFile; fileRef = AF5E68832C9BD7E9008DBC6A /* VideoTextDetectorUITests.swift */; };
		AF5E68862C9BD7E9008DBC6A /* VideoTextDetectorUITestsLaunchTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = AF5E68852C9BD7E9008DBC6A /* VideoTextDetectorUITestsLaunchTests.swift */; };
		AF5E68942C9BD90B008DBC6A /* Vision.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = AF5E68932C9BD90B008DBC6A /* Vision.framework */; };
		AF5E68962C9BD915008DBC6A /* AVFoundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = AF5E68952C9BD915008DBC6A /* AVFoundation.framework */; };
		AF73D6472DF282BA00A2E165 /* LicenseManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = AF73D6462DF282BA00A2E165 /* LicenseManager.swift */; };
		AF807F822CD4B9DE00F748E9 /* Translation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = AF807F812CD4B9DE00F748E9 /* Translation.framework */; };
		AF80BE6B2DFD2F8500F7CE7D /* InfoPlist.strings in Resources */ = {isa = PBXBuildFile; fileRef = AF80BE692DFD2F8500F7CE7D /* InfoPlist.strings */; };
		AF80BE752DFE948500F7CE7D /* StoreKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = AF80BE742DFE948500F7CE7D /* StoreKit.framework */; };
		AFA04E112E0F68C90068FBBA /* Localizable.xcstrings in Resources */ = {isa = PBXBuildFile; fileRef = AFA04E102E0F68C90068FBBA /* Localizable.xcstrings */; };
		AFA247BD2DF65C2A0005B00B /* StoreView.swift in Sources */ = {isa = PBXBuildFile; fileRef = AFA247BC2DF65C290005B00B /* StoreView.swift */; };
		AFA247BE2DF65C2A0005B00B /* StoreManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = AFA247BB2DF65C290005B00B /* StoreManager.swift */; };
		AFA99FD02D2A2A6A00E87093 /* WhisperKit in Frameworks */ = {isa = PBXBuildFile; productRef = AFA99FCF2D2A2A6A00E87093 /* WhisperKit */; };
		AFA99FD22D2A2A6A00E87093 /* whisperkit-cli in Frameworks */ = {isa = PBXBuildFile; productRef = AFA99FD12D2A2A6A00E87093 /* whisperkit-cli */; };
		AFA99FD52D2A2DDE00E87093 /* WhisperKit in Frameworks */ = {isa = PBXBuildFile; productRef = AFA99FD42D2A2DDE00E87093 /* WhisperKit */; };
		AFA99FD72D2A2DDE00E87093 /* whisperkit-cli in Frameworks */ = {isa = PBXBuildFile; productRef = AFA99FD62D2A2DDE00E87093 /* whisperkit-cli */; };
		AFB899FC2D1FD4D000FC5A0A /* ContentView.swift in Sources */ = {isa = PBXBuildFile; fileRef = AFB899FB2D1FD4D000FC5A0A /* ContentView.swift */; };
		AFCAE6AF2DA10EA700589472 /* SettingsView.swift in Sources */ = {isa = PBXBuildFile; fileRef = AFCAE6AE2DA10EA700589472 /* SettingsView.swift */; };
		AFE603A22CA53C4E00546E70 /* CoreML.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = AFE603A12CA53C4E00546E70 /* CoreML.framework */; };
		AFFA933C2D1F979800FED275 /* Config.swift in Sources */ = {isa = PBXBuildFile; fileRef = AFFA933B2D1F979800FED275 /* Config.swift */; };
		AFFA93762D1FBDFA00FED275 /* VideoProcessor.swift in Sources */ = {isa = PBXBuildFile; fileRef = AFFA93752D1FBDFA00FED275 /* VideoProcessor.swift */; };
		AFFA93772D1FBDFA00FED275 /* VideoPlayer.swift in Sources */ = {isa = PBXBuildFile; fileRef = AFFA93742D1FBDFA00FED275 /* VideoPlayer.swift */; };
		AFFA93792D1FBDFA00FED275 /* AITranslationView.swift in Sources */ = {isa = PBXBuildFile; fileRef = AFFA93702D1FBDFA00FED275 /* AITranslationView.swift */; };
		AFFA937A2D1FBDFA00FED275 /* AudioPlayer.swift in Sources */ = {isa = PBXBuildFile; fileRef = AFFA93712D1FBDFA00FED275 /* AudioPlayer.swift */; };
/* End PBXBuildFile section */

/* Begin PBXBuildRule section */
		AFDBBCD02CA79AFA0087B143 /* PBXBuildRule */ = {
			isa = PBXBuildRule;
			compilerSpec = com.apple.compilers.proxy.script;
			fileType = folder.mlpackage;
			inputFiles = (
			);
			isEditable = 1;
			outputFiles = (
			);
			script = "# coremlc\n";
		};
/* End PBXBuildRule section */

/* Begin PBXContainerItemProxy section */
		AF5E68762C9BD7E9008DBC6A /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = AF5E685C2C9BD7E8008DBC6A /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = AF5E68632C9BD7E8008DBC6A;
			remoteInfo = VideoTextDetector;
		};
		AF5E68802C9BD7E9008DBC6A /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = AF5E685C2C9BD7E8008DBC6A /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = AF5E68632C9BD7E8008DBC6A;
			remoteInfo = VideoTextDetector;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		AF5E68642C9BD7E8008DBC6A /* MocaSubtitle.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = MocaSubtitle.app; sourceTree = BUILT_PRODUCTS_DIR; };
		AF5E68672C9BD7E8008DBC6A /* VideoTextDetectorApp.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = VideoTextDetectorApp.swift; sourceTree = "<group>"; };
		AF5E686B2C9BD7E9008DBC6A /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		AF5E686E2C9BD7E9008DBC6A /* Preview Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = "Preview Assets.xcassets"; sourceTree = "<group>"; };
		AF5E68702C9BD7E9008DBC6A /* VideoTextDetector.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = VideoTextDetector.entitlements; sourceTree = "<group>"; };
		AF5E68752C9BD7E9008DBC6A /* MocaSubtitleTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = MocaSubtitleTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		AF5E68792C9BD7E9008DBC6A /* VideoTextDetectorTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = VideoTextDetectorTests.swift; sourceTree = "<group>"; };
		AF5E687F2C9BD7E9008DBC6A /* MocaSubtitleUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = MocaSubtitleUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		AF5E68832C9BD7E9008DBC6A /* VideoTextDetectorUITests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = VideoTextDetectorUITests.swift; sourceTree = "<group>"; };
		AF5E68852C9BD7E9008DBC6A /* VideoTextDetectorUITestsLaunchTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = VideoTextDetectorUITestsLaunchTests.swift; sourceTree = "<group>"; };
		AF5E68932C9BD90B008DBC6A /* Vision.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Vision.framework; path = System/Library/Frameworks/Vision.framework; sourceTree = SDKROOT; };
		AF5E68952C9BD915008DBC6A /* AVFoundation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AVFoundation.framework; path = System/Library/Frameworks/AVFoundation.framework; sourceTree = SDKROOT; };
		AF73D6462DF282BA00A2E165 /* LicenseManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LicenseManager.swift; sourceTree = "<group>"; };
		AF807F812CD4B9DE00F748E9 /* Translation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Translation.framework; path = System/Library/Frameworks/Translation.framework; sourceTree = SDKROOT; };
		AF80BE672DFD2C6F00F7CE7D /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		AF80BE6D2DFD307D00F7CE7D /* zh-Hans-CN */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "zh-Hans-CN"; path = "zh-Hans-CN.lproj/InfoPlist.strings"; sourceTree = "<group>"; };
		AF80BE742DFE948500F7CE7D /* StoreKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = StoreKit.framework; path = System/Library/Frameworks/StoreKit.framework; sourceTree = SDKROOT; };
		AF8B2E092DA51B4000368841 /* QucikCaption.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; path = QucikCaption.xcodeproj; sourceTree = "<group>"; };
		AFA04E102E0F68C90068FBBA /* Localizable.xcstrings */ = {isa = PBXFileReference; lastKnownFileType = text.json.xcstrings; path = Localizable.xcstrings; sourceTree = "<group>"; };
		AFA247BB2DF65C290005B00B /* StoreManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = StoreManager.swift; sourceTree = "<group>"; };
		AFA247BC2DF65C290005B00B /* StoreView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = StoreView.swift; sourceTree = "<group>"; };
		AFB899FB2D1FD4D000FC5A0A /* ContentView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ContentView.swift; sourceTree = "<group>"; };
		AFCAE6AE2DA10EA700589472 /* SettingsView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SettingsView.swift; sourceTree = "<group>"; };
		AFE603A12CA53C4E00546E70 /* CoreML.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreML.framework; path = System/Library/Frameworks/CoreML.framework; sourceTree = SDKROOT; };
		AFFA933B2D1F979800FED275 /* Config.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Config.swift; sourceTree = "<group>"; };
		AFFA93702D1FBDFA00FED275 /* AITranslationView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AITranslationView.swift; sourceTree = "<group>"; };
		AFFA93712D1FBDFA00FED275 /* AudioPlayer.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AudioPlayer.swift; sourceTree = "<group>"; };
		AFFA93742D1FBDFA00FED275 /* VideoPlayer.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = VideoPlayer.swift; sourceTree = "<group>"; };
		AFFA93752D1FBDFA00FED275 /* VideoProcessor.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = VideoProcessor.swift; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		AFCD62012CFDA41F002C66CE /* whisperkit */ = {isa = PBXFileSystemSynchronizedRootGroup; explicitFileTypes = {}; explicitFolders = (); path = whisperkit; sourceTree = "<group>"; };
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		AF5E68612C9BD7E8008DBC6A /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				AF80BE752DFE948500F7CE7D /* StoreKit.framework in Frameworks */,
				AF807F822CD4B9DE00F748E9 /* Translation.framework in Frameworks */,
				AFE603A22CA53C4E00546E70 /* CoreML.framework in Frameworks */,
				AFA99FD02D2A2A6A00E87093 /* WhisperKit in Frameworks */,
				AF5E68962C9BD915008DBC6A /* AVFoundation.framework in Frameworks */,
				AF5E68942C9BD90B008DBC6A /* Vision.framework in Frameworks */,
				AFA99FD72D2A2DDE00E87093 /* whisperkit-cli in Frameworks */,
				AFA99FD22D2A2A6A00E87093 /* whisperkit-cli in Frameworks */,
				AFA99FD52D2A2DDE00E87093 /* WhisperKit in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		AF5E68722C9BD7E9008DBC6A /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		AF5E687C2C9BD7E9008DBC6A /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		AF5E685B2C9BD7E8008DBC6A = {
			isa = PBXGroup;
			children = (
				AF8B2E092DA51B4000368841 /* QucikCaption.xcodeproj */,
				AFCD62012CFDA41F002C66CE /* whisperkit */,
				AF5E68662C9BD7E8008DBC6A /* VideoTextDetector */,
				AF5E68782C9BD7E9008DBC6A /* VideoTextDetectorTests */,
				AF5E68822C9BD7E9008DBC6A /* VideoTextDetectorUITests */,
				AF5E68652C9BD7E8008DBC6A /* Products */,
				AF5E68922C9BD90B008DBC6A /* Frameworks */,
			);
			sourceTree = "<group>";
		};
		AF5E68652C9BD7E8008DBC6A /* Products */ = {
			isa = PBXGroup;
			children = (
				AF5E68642C9BD7E8008DBC6A /* MocaSubtitle.app */,
				AF5E68752C9BD7E9008DBC6A /* MocaSubtitleTests.xctest */,
				AF5E687F2C9BD7E9008DBC6A /* MocaSubtitleUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		AF5E68662C9BD7E8008DBC6A /* VideoTextDetector */ = {
			isa = PBXGroup;
			children = (
				AFA04E102E0F68C90068FBBA /* Localizable.xcstrings */,
				AF80BE672DFD2C6F00F7CE7D /* Info.plist */,
				AFA247BB2DF65C290005B00B /* StoreManager.swift */,
				AFA247BC2DF65C290005B00B /* StoreView.swift */,
				AFCAE6AE2DA10EA700589472 /* SettingsView.swift */,
				AFB899FB2D1FD4D000FC5A0A /* ContentView.swift */,
				AFFA93702D1FBDFA00FED275 /* AITranslationView.swift */,
				AFFA93712D1FBDFA00FED275 /* AudioPlayer.swift */,
				AFFA93742D1FBDFA00FED275 /* VideoPlayer.swift */,
				AFFA93752D1FBDFA00FED275 /* VideoProcessor.swift */,
				AF5E68672C9BD7E8008DBC6A /* VideoTextDetectorApp.swift */,
				AF5E686B2C9BD7E9008DBC6A /* Assets.xcassets */,
				AF5E68702C9BD7E9008DBC6A /* VideoTextDetector.entitlements */,
				AF5E686D2C9BD7E9008DBC6A /* Preview Content */,
				AFFA933B2D1F979800FED275 /* Config.swift */,
				AF73D6462DF282BA00A2E165 /* LicenseManager.swift */,
				AF80BE692DFD2F8500F7CE7D /* InfoPlist.strings */,
			);
			path = VideoTextDetector;
			sourceTree = "<group>";
		};
		AF5E686D2C9BD7E9008DBC6A /* Preview Content */ = {
			isa = PBXGroup;
			children = (
				AF5E686E2C9BD7E9008DBC6A /* Preview Assets.xcassets */,
			);
			path = "Preview Content";
			sourceTree = "<group>";
		};
		AF5E68782C9BD7E9008DBC6A /* VideoTextDetectorTests */ = {
			isa = PBXGroup;
			children = (
				AF5E68792C9BD7E9008DBC6A /* VideoTextDetectorTests.swift */,
			);
			path = VideoTextDetectorTests;
			sourceTree = "<group>";
		};
		AF5E68822C9BD7E9008DBC6A /* VideoTextDetectorUITests */ = {
			isa = PBXGroup;
			children = (
				AF5E68832C9BD7E9008DBC6A /* VideoTextDetectorUITests.swift */,
				AF5E68852C9BD7E9008DBC6A /* VideoTextDetectorUITestsLaunchTests.swift */,
			);
			path = VideoTextDetectorUITests;
			sourceTree = "<group>";
		};
		AF5E68922C9BD90B008DBC6A /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				AF80BE742DFE948500F7CE7D /* StoreKit.framework */,
				AF807F812CD4B9DE00F748E9 /* Translation.framework */,
				AFE603A12CA53C4E00546E70 /* CoreML.framework */,
				AF5E68952C9BD915008DBC6A /* AVFoundation.framework */,
				AF5E68932C9BD90B008DBC6A /* Vision.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		AF8B2E0A2DA51B4000368841 /* Products */ = {
			isa = PBXGroup;
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		AF5E68632C9BD7E8008DBC6A /* MocaSubtitle */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = AF5E68892C9BD7E9008DBC6A /* Build configuration list for PBXNativeTarget "MocaSubtitle" */;
			buildPhases = (
				AF5E68602C9BD7E8008DBC6A /* Sources */,
				AF5E68612C9BD7E8008DBC6A /* Frameworks */,
				AF5E68622C9BD7E8008DBC6A /* Resources */,
			);
			buildRules = (
				AFDBBCD02CA79AFA0087B143 /* PBXBuildRule */,
			);
			dependencies = (
			);
			name = MocaSubtitle;
			productName = VideoTextDetector;
			productReference = AF5E68642C9BD7E8008DBC6A /* MocaSubtitle.app */;
			productType = "com.apple.product-type.application";
		};
		AF5E68742C9BD7E9008DBC6A /* MocaSubtitleTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = AF5E688C2C9BD7E9008DBC6A /* Build configuration list for PBXNativeTarget "MocaSubtitleTests" */;
			buildPhases = (
				AF5E68712C9BD7E9008DBC6A /* Sources */,
				AF5E68722C9BD7E9008DBC6A /* Frameworks */,
				AF5E68732C9BD7E9008DBC6A /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				AF5E68772C9BD7E9008DBC6A /* PBXTargetDependency */,
			);
			name = MocaSubtitleTests;
			productName = VideoTextDetectorTests;
			productReference = AF5E68752C9BD7E9008DBC6A /* MocaSubtitleTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		AF5E687E2C9BD7E9008DBC6A /* MocaSubtitleUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = AF5E688F2C9BD7E9008DBC6A /* Build configuration list for PBXNativeTarget "MocaSubtitleUITests" */;
			buildPhases = (
				AF5E687B2C9BD7E9008DBC6A /* Sources */,
				AF5E687C2C9BD7E9008DBC6A /* Frameworks */,
				AF5E687D2C9BD7E9008DBC6A /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				AF5E68812C9BD7E9008DBC6A /* PBXTargetDependency */,
			);
			name = MocaSubtitleUITests;
			productName = VideoTextDetectorUITests;
			productReference = AF5E687F2C9BD7E9008DBC6A /* MocaSubtitleUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		AF5E685C2C9BD7E8008DBC6A /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1540;
				LastUpgradeCheck = 1600;
				TargetAttributes = {
					AF5E68632C9BD7E8008DBC6A = {
						CreatedOnToolsVersion = 15.4;
					};
					AF5E68742C9BD7E9008DBC6A = {
						CreatedOnToolsVersion = 15.4;
						TestTargetID = AF5E68632C9BD7E8008DBC6A;
					};
					AF5E687E2C9BD7E9008DBC6A = {
						CreatedOnToolsVersion = 15.4;
						TestTargetID = AF5E68632C9BD7E8008DBC6A;
					};
				};
			};
			buildConfigurationList = AF5E685F2C9BD7E8008DBC6A /* Build configuration list for PBXProject "QucikCaption" */;
			compatibilityVersion = "Xcode 15.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
				"zh-Hans-CN",
			);
			mainGroup = AF5E685B2C9BD7E8008DBC6A;
			packageReferences = (
				AFA99FD32D2A2DDE00E87093 /* XCRemoteSwiftPackageReference "WhisperKit" */,
			);
			productRefGroup = AF5E68652C9BD7E8008DBC6A /* Products */;
			projectDirPath = "";
			projectReferences = (
				{
					ProductGroup = AF8B2E0A2DA51B4000368841 /* Products */;
					ProjectRef = AF8B2E092DA51B4000368841 /* QucikCaption.xcodeproj */;
				},
			);
			projectRoot = "";
			targets = (
				AF5E68632C9BD7E8008DBC6A /* MocaSubtitle */,
				AF5E68742C9BD7E9008DBC6A /* MocaSubtitleTests */,
				AF5E687E2C9BD7E9008DBC6A /* MocaSubtitleUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		AF5E68622C9BD7E8008DBC6A /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				AF5E686F2C9BD7E9008DBC6A /* Preview Assets.xcassets in Resources */,
				AF5E686C2C9BD7E9008DBC6A /* Assets.xcassets in Resources */,
				AFA04E112E0F68C90068FBBA /* Localizable.xcstrings in Resources */,
				AF80BE6B2DFD2F8500F7CE7D /* InfoPlist.strings in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		AF5E68732C9BD7E9008DBC6A /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		AF5E687D2C9BD7E9008DBC6A /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		AF5E68602C9BD7E8008DBC6A /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				AFFA933C2D1F979800FED275 /* Config.swift in Sources */,
				AFCAE6AF2DA10EA700589472 /* SettingsView.swift in Sources */,
				AF5E68682C9BD7E8008DBC6A /* VideoTextDetectorApp.swift in Sources */,
				AFA247BD2DF65C2A0005B00B /* StoreView.swift in Sources */,
				AFA247BE2DF65C2A0005B00B /* StoreManager.swift in Sources */,
				AFB899FC2D1FD4D000FC5A0A /* ContentView.swift in Sources */,
				AF73D6472DF282BA00A2E165 /* LicenseManager.swift in Sources */,
				AFFA93762D1FBDFA00FED275 /* VideoProcessor.swift in Sources */,
				AFFA93772D1FBDFA00FED275 /* VideoPlayer.swift in Sources */,
				AFFA93792D1FBDFA00FED275 /* AITranslationView.swift in Sources */,
				AFFA937A2D1FBDFA00FED275 /* AudioPlayer.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		AF5E68712C9BD7E9008DBC6A /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				AF5E687A2C9BD7E9008DBC6A /* VideoTextDetectorTests.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		AF5E687B2C9BD7E9008DBC6A /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				AF5E68842C9BD7E9008DBC6A /* VideoTextDetectorUITests.swift in Sources */,
				AF5E68862C9BD7E9008DBC6A /* VideoTextDetectorUITestsLaunchTests.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		AF5E68772C9BD7E9008DBC6A /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = AF5E68632C9BD7E8008DBC6A /* MocaSubtitle */;
			targetProxy = AF5E68762C9BD7E9008DBC6A /* PBXContainerItemProxy */;
		};
		AF5E68812C9BD7E9008DBC6A /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = AF5E68632C9BD7E8008DBC6A /* MocaSubtitle */;
			targetProxy = AF5E68802C9BD7E9008DBC6A /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin PBXVariantGroup section */
		AF80BE692DFD2F8500F7CE7D /* InfoPlist.strings */ = {
			isa = PBXVariantGroup;
			children = (
				AF80BE6D2DFD307D00F7CE7D /* zh-Hans-CN */,
			);
			name = InfoPlist.strings;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		AF5E68872C9BD7E9008DBC6A /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				COREML_COMPILER = "";
				"COREML_COMPILER[arch=*]" = "";
				DEAD_CODE_STRIPPING = YES;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MACOSX_DEPLOYMENT_TARGET = 14.3;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = macosx;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		AF5E68882C9BD7E9008DBC6A /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				COREML_COMPILER = "";
				DEAD_CODE_STRIPPING = YES;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MACOSX_DEPLOYMENT_TARGET = 14.3;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = macosx;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_EMIT_LOC_STRINGS = YES;
			};
			name = Release;
		};
		AF5E688A2C9BD7E9008DBC6A /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ASSETCATALOG_COMPILER_INCLUDE_ALL_APPICON_ASSETS = NO;
				CODE_SIGN_ENTITLEMENTS = VideoTextDetector/VideoTextDetector.entitlements;
				"CODE_SIGN_IDENTITY[sdk=macosx*]" = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 9;
				DEAD_CODE_STRIPPING = YES;
				DEVELOPMENT_ASSET_PATHS = "\"VideoTextDetector/Preview Content\"";
				DEVELOPMENT_TEAM = J2KLM3UFGQ;
				ENABLE_HARDENED_RUNTIME = YES;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_CFBundleDisplayName = MocaSubtitle;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.productivity";
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
				);
				MACOSX_DEPLOYMENT_TARGET = 15.0;
				MARKETING_VERSION = 1.2.1;
				PRODUCT_BUNDLE_IDENTIFIER = top.emmaflow.MocaSubtitle;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
			};
			name = Debug;
		};
		AF5E688B2C9BD7E9008DBC6A /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ASSETCATALOG_COMPILER_INCLUDE_ALL_APPICON_ASSETS = NO;
				CODE_SIGN_ENTITLEMENTS = VideoTextDetector/VideoTextDetector.entitlements;
				"CODE_SIGN_IDENTITY[sdk=macosx*]" = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 9;
				DEAD_CODE_STRIPPING = YES;
				DEVELOPMENT_ASSET_PATHS = "\"VideoTextDetector/Preview Content\"";
				DEVELOPMENT_TEAM = J2KLM3UFGQ;
				ENABLE_HARDENED_RUNTIME = YES;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_CFBundleDisplayName = MocaSubtitle;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.productivity";
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
				);
				MACOSX_DEPLOYMENT_TARGET = 15.0;
				MARKETING_VERSION = 1.2.1;
				PRODUCT_BUNDLE_IDENTIFIER = top.emmaflow.MocaSubtitle;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
			};
			name = Release;
		};
		AF5E688D2C9BD7E9008DBC6A /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEAD_CODE_STRIPPING = YES;
				DEVELOPMENT_TEAM = J2KLM3UFGQ;
				GENERATE_INFOPLIST_FILE = YES;
				MACOSX_DEPLOYMENT_TARGET = 14.3;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.VideoTextDetectorTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/MocaSubtitle.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/MocaSubtitle";
			};
			name = Debug;
		};
		AF5E688E2C9BD7E9008DBC6A /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEAD_CODE_STRIPPING = YES;
				DEVELOPMENT_TEAM = J2KLM3UFGQ;
				GENERATE_INFOPLIST_FILE = YES;
				MACOSX_DEPLOYMENT_TARGET = 14.3;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.VideoTextDetectorTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/MocaSubtitle.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/MocaSubtitle";
			};
			name = Release;
		};
		AF5E68902C9BD7E9008DBC6A /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEAD_CODE_STRIPPING = YES;
				DEVELOPMENT_TEAM = J2KLM3UFGQ;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.VideoTextDetectorUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TEST_TARGET_NAME = VideoTextDetector;
			};
			name = Debug;
		};
		AF5E68912C9BD7E9008DBC6A /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEAD_CODE_STRIPPING = YES;
				DEVELOPMENT_TEAM = J2KLM3UFGQ;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.VideoTextDetectorUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TEST_TARGET_NAME = VideoTextDetector;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		AF5E685F2C9BD7E8008DBC6A /* Build configuration list for PBXProject "QucikCaption" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				AF5E68872C9BD7E9008DBC6A /* Debug */,
				AF5E68882C9BD7E9008DBC6A /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		AF5E68892C9BD7E9008DBC6A /* Build configuration list for PBXNativeTarget "MocaSubtitle" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				AF5E688A2C9BD7E9008DBC6A /* Debug */,
				AF5E688B2C9BD7E9008DBC6A /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		AF5E688C2C9BD7E9008DBC6A /* Build configuration list for PBXNativeTarget "MocaSubtitleTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				AF5E688D2C9BD7E9008DBC6A /* Debug */,
				AF5E688E2C9BD7E9008DBC6A /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		AF5E688F2C9BD7E9008DBC6A /* Build configuration list for PBXNativeTarget "MocaSubtitleUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				AF5E68902C9BD7E9008DBC6A /* Debug */,
				AF5E68912C9BD7E9008DBC6A /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */

/* Begin XCRemoteSwiftPackageReference section */
		AFA99FCE2D2A2A6A00E87093 /* XCRemoteSwiftPackageReference "WhisperKit" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/argmaxinc/WhisperKit";
			requirement = {
				branch = main;
				kind = branch;
			};
		};
		AFA99FD32D2A2DDE00E87093 /* XCRemoteSwiftPackageReference "WhisperKit" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/argmaxinc/WhisperKit";
			requirement = {
				kind = exactVersion;
				version = 0.13.0;
			};
		};
/* End XCRemoteSwiftPackageReference section */

/* Begin XCSwiftPackageProductDependency section */
		AFA99FCF2D2A2A6A00E87093 /* WhisperKit */ = {
			isa = XCSwiftPackageProductDependency;
			package = AFA99FCE2D2A2A6A00E87093 /* XCRemoteSwiftPackageReference "WhisperKit" */;
			productName = WhisperKit;
		};
		AFA99FD12D2A2A6A00E87093 /* whisperkit-cli */ = {
			isa = XCSwiftPackageProductDependency;
			package = AFA99FCE2D2A2A6A00E87093 /* XCRemoteSwiftPackageReference "WhisperKit" */;
			productName = "whisperkit-cli";
		};
		AFA99FD42D2A2DDE00E87093 /* WhisperKit */ = {
			isa = XCSwiftPackageProductDependency;
			package = AFA99FD32D2A2DDE00E87093 /* XCRemoteSwiftPackageReference "WhisperKit" */;
			productName = WhisperKit;
		};
		AFA99FD62D2A2DDE00E87093 /* whisperkit-cli */ = {
			isa = XCSwiftPackageProductDependency;
			package = AFA99FD32D2A2DDE00E87093 /* XCRemoteSwiftPackageReference "WhisperKit" */;
			productName = "whisperkit-cli";
		};
/* End XCSwiftPackageProductDependency section */
	};
	rootObject = AF5E685C2C9BD7E8008DBC6A /* Project object */;
}
