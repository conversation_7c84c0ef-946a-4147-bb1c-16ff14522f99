import SwiftUI
import AVKit

// 字幕显示模式
enum SubtitleMode: String, CaseIterable, Identifiable {
    case bilingual
    case original
    case translation
    case none
    
    var id: String { self.rawValue }

    // 1. 新增一个计算属性来返回每个 case 对应的“钥匙”
    private var localizationKey: String {
        switch self {
        case .bilingual:
            return "subtitle_mode_bilingual" // 双语字幕的钥匙
        case .original:
            return "subtitle_mode_original"  // 原始字幕的钥匙
        case .translation:
            return "subtitle_mode_translation" // 翻译字幕的钥匙
        case .none:
            return "subtitle_mode_none"      // 无字幕的钥匙
        }
    }
    
    // 2. 修改您原有的 localized 属性，让它使用新的 key
    var localized: LocalizedStringKey {
        return LocalizedStringKey(self.localizationKey)
    }
}

struct VideoPlayer: NSViewRepresentable {
    var url: URL
    var player: AVPlayer
    var segments: [EditableSegment]  // 使用 optimizedSegments
    var currentTime: Double
    @Binding var subtitleMode: SubtitleMode
    var detectionBoxes: [CGRect] = [] // 检测框数组
    var showDetectionBoxes: Bool = false // 是否显示检测框
    
    func makeNSView(context: Context) -> AVPlayerView {
        let view = AVPlayerView()
        view.player = player
        view.controlsStyle = .inline
        view.showsFullScreenToggleButton = true
        
        // 创建字幕文本视图，使用NSTextView代替NSTextField以获得更好的换行支持，yes
        let textView = NSTextView()
        textView.isEditable = false // 禁止编辑
        textView.drawsBackground = false // 不显示背景
        textView.textColor = NSColor.white // 设置字体颜色
        textView.isSelectable = false // 禁止选择
        textView.textContainer?.lineFragmentPadding = 1 // 设置行间距
        textView.textContainerInset = NSSize(width: 16, height: 6) // 设置内边距
        
        // 设置圆角
        textView.wantsLayer = true
        textView.layer?.cornerRadius = 4
        textView.layer?.masksToBounds = true
        
        // 设置默认字体
        let defaultFont = NSFont.systemFont(ofSize: 14, weight: .semibold)
        textView.font = defaultFont
        
        // 自动布局设置
        textView.autoresizingMask = [.width]
        textView.textContainer?.widthTracksTextView = true
        textView.textContainer?.heightTracksTextView = false
        textView.layoutManager?.allowsNonContiguousLayout = true
        
        // 将字幕视图添加到播放器视图
        view.addSubview(textView)
        context.coordinator.subtitleOverlay = textView
        
        // 注册时间观察器
        context.coordinator.setupTimeObserver()
        
        return view
    }
    
    func updateNSView(_ nsView: AVPlayerView, context: Context) {
        // 更新字幕和段落数据
        context.coordinator.updateSegments(segments)
        context.coordinator.updateSubtitle(at: currentTime)
    }
    
    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }
    
    class Coordinator: NSObject {
        var parent: VideoPlayer
        var timeObserver: Any?
        var subtitleOverlay: NSTextView?
        var lastSubtitleId: UUID?  // 跟踪当前显示的字幕ID
        private var currentSegments: [EditableSegment] = []  // 添加这行
        var detectionBoxOverlays: [NSView] = [] // 检测框覆盖层
        
        init(_ parent: VideoPlayer) {
            self.parent = parent
            self.currentSegments = parent.segments  // 初始化段落数据
            super.init()
        }
        
        func setupTimeObserver() {
            if let existingObserver = timeObserver {
                parent.player.removeTimeObserver(existingObserver)
            }
            
            // 使用更高的时间刻度确保毫秒级精度
            let interval = CMTime(seconds: 0.05, preferredTimescale: 1000)
            timeObserver = parent.player.addPeriodicTimeObserver(
                forInterval: interval,
                queue: .main
            ) { [weak self] time in
                self?.updateSubtitle(at: CMTimeGetSeconds(time))
            }
        }
        
        // 添加更新段落的方法
        func updateSegments(_ newSegments: [EditableSegment]) {
            // 检查段落是否发生变化
            let segmentsChanged = newSegments.count != currentSegments.count ||
                zip(newSegments, currentSegments).contains { new, current in
                    new.id != current.id ||
                    new.text != current.text ||
                    new.translatedText != current.translatedText
                }
            
            if segmentsChanged {
                currentSegments = newSegments
                // 强制更新当前字幕
                lastSubtitleId = nil  // 重置lastSubtitleId以强制更新
                updateSubtitle(at: CMTimeGetSeconds(parent.player.currentTime()))
            }
        }
        
        func updateSubtitle(at time: Double) {
            // 查找当前时间对应的字幕段落
            if let currentSegment = currentSegments.first(where: { 
                time >= $0.startTime && time <= $0.endTime 
            }) {
                // 如果是同一个字幕且内容没有变化，不需要更新
                if lastSubtitleId == currentSegment.id {
                    return
                }
                
                lastSubtitleId = currentSegment.id
                
                // 准备字幕文本
                var subtitleText = ""
                switch parent.subtitleMode {
                case .bilingual:
                    if let translatedText = currentSegment.translatedText {
                        subtitleText = "\(translatedText)\n\(currentSegment.text)"
                    } else {
                        subtitleText = currentSegment.text
                    }
                case .original:
                    subtitleText = currentSegment.text
                case .translation:
                    subtitleText = currentSegment.translatedText ?? currentSegment.text
                case .none:
                    subtitleText = ""
                }
                
                // 在主线程更新UI
                DispatchQueue.main.async { [weak self] in
                    guard let textView = self?.subtitleOverlay as? NSTextView,
                          let playerView = textView.superview as? AVPlayerView else { return }
                    
                    // 获取视频区域
                    let videoRect = playerView.videoBounds
                    let maxWidth = videoRect.width * 0.95
                    
                    // 创建段落样式
                    let paragraphStyle = NSMutableParagraphStyle()
                    paragraphStyle.alignment = .center
                    paragraphStyle.lineSpacing = 0.2 // 行间距
                    paragraphStyle.paragraphSpacing = 0 // 段落间距
                    paragraphStyle.lineBreakMode = .byWordWrapping
                    
                    // 创建富文本
                    let attributedString = NSMutableAttributedString()

                    // for family in NSFontManager.shared.availableFontFamilies {
                    //     print("family:\(family)")
                    // }
                    
                    // 分别设置翻译和原文的样式
                    let lines = subtitleText.components(separatedBy: "\n")
                    for (index, line) in lines.enumerated() {
                        if index > 0 {
                            attributedString.append(NSAttributedString(string: "\n"))
                        }
                        
                        let lineText = line.trimmingCharacters(in: .whitespacesAndNewlines)
                        let isTranslation = index == 0 // 第一行是翻译，第二行是原文
                        
                        // 根据是翻译还是原文设置样式
                        let fontSize: CGFloat = isTranslation ? 14 : 12
                        let fontName = "Source Han Sans SC Heavy" // 统一使用思源黑体
                        let font = NSFont(name: fontName, size: fontSize) ?? NSFont.systemFont(ofSize: fontSize, weight: .semibold)
                        
                        let attrs: [NSAttributedString.Key: Any] = [
                            .font: font,
                            .foregroundColor: isTranslation ? NSColor(red: 255/255, green: 222/255, blue: 0/255, alpha: 1) : NSColor.white, 
                            .paragraphStyle: paragraphStyle,
                            .strokeColor: NSColor.black,
                            .strokeWidth: -4
                        ]
                        
                        attributedString.append(NSAttributedString(string: line, attributes: attrs))
                    }
                    
                    // 设置富文本内容
                    textView.textStorage?.setAttributedString(attributedString)
                    
                    // 根据内容调整文本视图大小
                    let paddingWidth: CGFloat = 32  // 水平内边距
                    let paddingHeight: CGFloat = 20  // 垂直内边距
                    
                    // 设置文本容器宽度
                    textView.textContainer?.containerSize = NSSize(width: maxWidth - paddingWidth, height: CGFloat.greatestFiniteMagnitude)
                    
                    // 计算文本视图的理想高度
                    let contentHeight = textView.layoutManager?.usedRect(for: textView.textContainer!).height ?? 0
                    
                    // 设置文本视图的尺寸和位置
                    let finalWidth = min(maxWidth, textView.layoutManager?.usedRect(for: textView.textContainer!).width ?? 0) + paddingWidth
                    let finalHeight = contentHeight + paddingHeight
                    
                    // 计算位置 - 调整底部边距，确保始终可见
                    let bottomMargin: CGFloat = 5 // 增加距离视频画面底部的边距
                    let y = videoRect.minY + bottomMargin
                    let x = videoRect.minX + (videoRect.width - finalWidth) / 2
                    
                    // 设置最终frame
                    textView.frame = NSRect(
                        x: x,
                        y: y,
                        width: finalWidth,
                        height: finalHeight
                    )
                    
                    // 确保字幕层在最上面
                    textView.layer?.zPosition = 999
                    
                    // 优化字幕过渡 - 使用更快的淡入效果减少视觉延迟
                    NSAnimationContext.runAnimationGroup({ context in
                        context.duration = 0.2 // 减少动画时间
                        context.timingFunction = CAMediaTimingFunction(name: .easeOut)
                        textView.animator().alphaValue = 1
                    })
                }
            } else {
                // 如果没有找到字幕，平滑隐藏当前字幕
                lastSubtitleId = nil
                DispatchQueue.main.async { [weak self] in
                    guard let textView = self?.subtitleOverlay else { return }
                    NSAnimationContext.runAnimationGroup({ context in
                        context.duration = 0.2  // 减少动画时间
                        context.timingFunction = CAMediaTimingFunction(name: .easeIn)
                        textView.animator().alphaValue = 0
                    })
                }
            }
        }
        
        func isChineseText(_ text: String) -> Bool {
            return text.range(of: "\\p{Han}", options: .regularExpression) != nil
        }
        
        deinit {
            // 清理观察器
            if let observer = timeObserver {
                parent.player.removeTimeObserver(observer)
            }
            
            // 移除字幕叠加层
            subtitleOverlay?.removeFromSuperview()
        }
    }
}

// 字幕控制按钮
struct SubtitleControlButton: View {
    @Binding var subtitleMode: SubtitleMode
    
    var body: some View {
        Menu {
            // 字幕语言选项
            Section() {
                Button("显示原始字幕") {
                    subtitleMode = .original
                }
                .disabled(subtitleMode == .original)
                
                Button("显示翻译字幕") {
                    subtitleMode = .translation
                }
                .disabled(subtitleMode == .translation)
                
                Button("显示双语字幕") {
                    subtitleMode = .bilingual
                }
                .disabled(subtitleMode == .bilingual)
                
                Button("关闭字幕") {
                    subtitleMode = .none
                }
                .disabled(subtitleMode == .none)
            }
        } label: {
            HStack {
                Image(systemName: "captions.bubble")
                Text(subtitleMode.localized)
            }
            .padding(.horizontal, 8)
            .padding(.vertical, 4)
            .background(Color.secondary.opacity(0.2))
            .cornerRadius(6)
        }
    }
}
