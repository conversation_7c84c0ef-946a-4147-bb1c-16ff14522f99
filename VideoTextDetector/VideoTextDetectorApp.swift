//
//  VideoTextDetectorApp.swift
//  VideoTextDetector
//
//  Created by lhr on 9/19/24.
//

import SwiftUI
import StoreKit

@main
struct VideoTextDetectorApp: App {
    @StateObject private var storeManager = StoreManager.shared
    
    var body: some Scene {
        WindowGroup {
            ContentView()
                .onAppear {
                    // 在应用启动时初始化存储管理器
                    Task {
                        await storeManager.requestProducts()
                    }
                }
                .onReceive(NotificationCenter.default.publisher(for: NSApplication.willTerminateNotification)) { _ in
                    // 在应用退出时保存缓存
                    ContentView.shared?.saveAllCache()
                }
        }
    }
}

// 在macOS上运行时需要添加这个扩展，确保应用可以退出
extension NSApplicationDelegate {
    func applicationShouldTerminateAfterLastWindowClosed(_ sender: NSApplication) -> Bool {
        return true
    }
}
