import Foundation
import Vision
import AVFoundation
import AppKit

struct TextSegment: Identifiable {
    let id = UUID()
    let text: String
    let startTime: Double
    let endTime: Double
    let frames: [DetectedFrame]
    let textBounds: CGRect
    let confidence: VNConfidence
}

class VideoProcessor: ObservableObject {
    @Published var detectedFrames: [DetectedFrame] = []
    @Published var textSegments: [TextSegment] = []
    @Published var isProcessing = false
    @Published var progress: Int = 0
    @Published var searchResults: [TextSegment] = []
    
    // 字幕区域选择状态
    struct SubtitleRegion {
        var rect: CGRect = .zero
        var isSelected: Bool = false
    }

    @Published var subtitleRegion = SubtitleRegion()
    @Published var subtitleSegments: [EditableSegment] = []
    
    func processVideo(
        url: URL, 
        recognitionLevel: VNRequestTextRecognitionLevel = .accurate,
        progressHandler: ((Double) -> Void)? = nil
    ) async throws -> [DetectedFrame] {
        await MainActor.run {
            self.isProcessing = true
            self.textSegments = []
            self.progress = 0
        }
        
        var detectedFrames: [DetectedFrame] = []
        var currentSegments: [String: (startTime: Double, frames: [DetectedFrame], textBounds: CGRect, confidence: VNConfidence)] = [:]
        
        let asset = AVAsset(url: url)
        let duration = try await asset.load(.duration)
        let durationSeconds = CMTimeGetSeconds(duration)
        print("视频总时长: \(durationSeconds)秒")
        
        // 每秒采样的帧数
        let frameRate: Double = 2
        let totalFrames = Int(durationSeconds * frameRate)
        
        let generator = AVAssetImageGenerator(asset: asset)
        generator.appliesPreferredTrackTransform = true
        generator.requestedTimeToleranceBefore = .zero
        generator.requestedTimeToleranceAfter = .zero
        
        // 遍历整个视频时长
        for frameIndex in 0..<totalFrames {
            let timeInSeconds = Double(frameIndex) / frameRate
            let time = CMTime(seconds: timeInSeconds, preferredTimescale: 600)
            
            do {
                let cgImage = try generator.copyCGImage(at: time, actualTime: nil)
                let nsImage = NSImage(cgImage: cgImage, size: NSSize(width: cgImage.width, height: cgImage.height))
                
                // 执行文本检测
                if let (detectedText, textBounds, confidence) = try await detectText(in: nsImage, recognitionLevel: recognitionLevel) {
                    let frame = DetectedFrame(
                        image: nsImage,
                        detectedText: detectedText,
                        timestamp: timeInSeconds,
                        textBounds: textBounds,
                        confidence: confidence
                    )
                    detectedFrames.append(frame)
                    updateTextSegments(frame: frame, currentSegments: &currentSegments)
                }
            } catch {
                print("处理第 \(frameIndex) 帧时出错: \(error)")
                continue
            }
            
            // 更新进度
            let progress = Double(frameIndex + 1) / Double(totalFrames) * 100
            progressHandler?(progress) // 调用进度回调
        }
        
        // 处理完成后更新文本段
        var newSegments: [TextSegment] = []
        for (text, segmentInfo) in currentSegments {
            let cleanText = text.split(separator: "_").first.map(String.init) ?? text
            
            let textSegment = TextSegment(
                text: cleanText,
                startTime: segmentInfo.startTime,
                endTime: segmentInfo.frames.last?.timestamp ?? segmentInfo.startTime,
                frames: segmentInfo.frames,
                textBounds: segmentInfo.textBounds,
                confidence: segmentInfo.confidence
            )
            newSegments.append(textSegment)
        }
        
        // 按时间排序
        newSegments.sort { $0.startTime < $1.startTime }
        
        await MainActor.run {
            self.textSegments = newSegments
            self.isProcessing = false
            print("视频处理完成，共检测到 \(newSegments.count) 个文本段落")
        }
        
        return detectedFrames
    }
    
    private func detectText(in image: NSImage, recognitionLevel: VNRequestTextRecognitionLevel) async throws -> (String, CGRect, VNConfidence)? {
        guard let cgImage = image.cgImage(forProposedRect: nil, context: nil, hints: nil) else {
            return nil
        }
        
        return try await withCheckedThrowingContinuation { continuation in
            let request = VNRecognizeTextRequest { request, error in
                if let error = error {
                    continuation.resume(throwing: error)
                    return
                }
                
                guard let observations = request.results as? [VNRecognizedTextObservation] else {
                    continuation.resume(returning: nil)
                    return
                }
                
                var detectedText = ""
                var unionRect = CGRect.zero
                var isFirst = true
                var maxConfidence: VNConfidence = 0.0
                
                for observation in observations {
                    if let candidate = observation.topCandidates(1).first {
                        if !isFirst {
                            detectedText += " "
                        }
                        detectedText += candidate.string
                        
                        // 更新最高置信度
                        maxConfidence = max(maxConfidence, candidate.confidence)
                        
                        // 转换边界框坐标
                        let boundingBox = observation.boundingBox
                        // Vision 框架使用的是左下角为原点的坐标系统
                        let convertedRect = CGRect(
                            x: boundingBox.origin.x,
                            y: boundingBox.origin.y, // 保持原始 y 坐标
                            width: boundingBox.width,
                            height: boundingBox.height
                        )
                        
                        if isFirst {
                            unionRect = convertedRect
                        } else {
                            unionRect = unionRect.union(convertedRect)
                        }
                        
                        isFirst = false
                    }
                }
                
                if !detectedText.isEmpty {
                    continuation.resume(returning: (detectedText, unionRect, maxConfidence))
                } else {
                    continuation.resume(returning: nil)
                }
            }
            
            // 配置文本识别请求
            request.recognitionLevel = recognitionLevel
            request.recognitionLanguages = ["zh-Hans", "en-US"]
            request.usesLanguageCorrection = true
            request.minimumTextHeight = 0.01
            request.automaticallyDetectsLanguage = true
            
            let handler = VNImageRequestHandler(cgImage: cgImage, options: [:])
            try? handler.perform([request])
        }
    }
    
    private func updateTextSegments(frame: DetectedFrame, currentSegments: inout [String: (startTime: Double, frames: [DetectedFrame], textBounds: CGRect, confidence: VNConfidence)]) {
        guard !frame.detectedText.isEmpty else { return }
        
        // 清理和标准化检测到的文本
        let cleanedText = frame.detectedText.trimmingCharacters(in: .whitespacesAndNewlines)
        
        // 检查是否是相同或相似的文本
        if let existingSegment = currentSegments.first(where: { key, value in
            // 使用更宽松的匹配条件，允许小的差异
            let existingText = String(key.split(separator: "_").first ?? "") // 转换为 String
            let similarity = calculateSimilarity(between: existingText, and: cleanedText)
            return similarity > 0.8 // 80% 相似度阈值
        })?.key {
            // 获取现有段落的信息
            if let existingValue = currentSegments[existingSegment] {
                if frame.timestamp - existingValue.frames.last!.timestamp < 2.0 {
                    // 创建更新后的值
                    var updatedValue = existingValue
                    updatedValue.frames.append(frame)
                    updatedValue.textBounds = existingValue.textBounds.union(frame.textBounds)
                    updatedValue.confidence = max(existingValue.confidence, frame.confidence)
                    
                    // 更新字典
                    currentSegments[existingSegment] = updatedValue
                } else {
                    // 如果时间间隔太大，创建新的段落
                    let newKey = "\(cleanedText)_\(frame.timestamp)"
                    currentSegments[newKey] = (
                        startTime: frame.timestamp,
                        frames: [frame],
                        textBounds: frame.textBounds,
                        confidence: frame.confidence
                    )
                }
            }
        } else {
            // 创建新的段落
            currentSegments[cleanedText] = (
                startTime: frame.timestamp,
                frames: [frame],
                textBounds: frame.textBounds,
                confidence: frame.confidence
            )
        }
    }
    
    // 计算文本相似度的辅助函数
    private func calculateSimilarity(between text1: String, and text2: String) -> Double {
        let set1 = Set(text1)
        let set2 = Set(text2)
        let intersection = set1.intersection(set2)
        let union = set1.union(set2)
        return Double(intersection.count) / Double(union.count)
    }
    
    // 添加边界框调整函数
    private func adjustBoundingBox(_ rect: CGRect, for imageSize: CGSize) -> CGRect {
        // Vision 框架返回的坐标是标准化的（0-1），需要转换为实际像素坐标
        let pixelRect = CGRect(
            x: rect.origin.x * imageSize.width,
            y: rect.origin.y * imageSize.height,
            width: rect.width * imageSize.width,
            height: rect.height * imageSize.height
        )
        
        // 应用额外的校正因子（可以根据实际效果微调）
        let correctionFactor: CGFloat = 1.05 // 稍微扩大边界框
        
        return CGRect(
            x: pixelRect.origin.x * correctionFactor,
            y: pixelRect.origin.y * correctionFactor,
            width: pixelRect.width * correctionFactor,
            height: pixelRect.height * correctionFactor
        )
    }
    
    // 更新搜索方法以支持文本段
    func searchKeyword(_ keyword: String) -> [TextSegment] {
        return textSegments.filter { $0.text.lowercased().contains(keyword.lowercased()) }
    }

    func countKeywords(keyword: String) -> Int {
        return textSegments.filter { $0.text.lowercased().contains(keyword.lowercased()) }.count
    }

    // 处理指定区域内的字幕
    func processSubtitleRegion(
        url: URL,
        region: CGRect,
        recognitionLevel: VNRequestTextRecognitionLevel = .accurate,
        progressHandler: ((Double) -> Void)? = nil
    ) async throws -> [EditableSegment] {
        await MainActor.run {
            self.isProcessing = true
            self.progress = 0
        }
        
        var subtitleSegments: [EditableSegment] = []
        var currentSegments: [String: (startTime: Double, frames: [DetectedFrame], textBounds: CGRect, confidence: VNConfidence, chineseText: String?, englishText: String?)] = [:]

        
        let asset = AVAsset(url: url)
        let duration = try await asset.load(.duration)
        let durationSeconds = CMTimeGetSeconds(duration)
        print("视频总时长: \(durationSeconds)秒")
        
        // 每秒采样的帧数 (可以调整采样率)
        let frameRate: Double = 2
        let totalFrames = Int(durationSeconds * frameRate)
        
        let generator = AVAssetImageGenerator(asset: asset)
        generator.appliesPreferredTrackTransform = true
        generator.requestedTimeToleranceBefore = .zero
        generator.requestedTimeToleranceAfter = .zero
        
        // 遍历整个视频时长
        for frameIndex in 0..<totalFrames {
            let timeInSeconds = Double(frameIndex) / frameRate
            let time = CMTime(seconds: timeInSeconds, preferredTimescale: 600)
            
            do {
                let cgImage = try generator.copyCGImage(at: time, actualTime: nil)
                let nsImage = NSImage(cgImage: cgImage, size: NSSize(width: cgImage.width, height: cgImage.height))
                
                // 先裁剪图像到选择的字幕区域
                guard let croppedImage = cropImage(nsImage, to: region) else {
                    continue
                }
                
                // 在裁剪后的图像上检测文本
                if let (detectedText, textBounds, confidence) = try await detectText(in: croppedImage, recognitionLevel: recognitionLevel) {
                    // 创建检测帧，注意：textBounds需要相对于原始图像进行调整
                    let adjustedTextBounds = CGRect(
                        x: region.origin.x + textBounds.origin.x * region.width,
                        y: region.origin.y + textBounds.origin.y * region.height,
                        width: textBounds.width * region.width,
                        height: textBounds.height * region.height
                    )
                    
                    // 分离中文和英文
                    let (chineseText, englishText) = separateChineseAndEnglish(detectedText)
                    
                    let frame = DetectedFrame(
                        image: nsImage,
                        detectedText: detectedText,
                        timestamp: timeInSeconds,
                        textBounds: adjustedTextBounds,
                        confidence: confidence
                    )
                    
                    // 使用修改后的updateTextSegments函数
                    updateTextSegments(frame: frame, 
                                      chineseText: chineseText, 
                                      englishText: englishText, 
                                      currentSegments: &currentSegments)
                }
            } catch {
                print("处理第 \(frameIndex) 帧时出错: \(error)")
                continue
            }
            
            // 更新进度
            let progress = Double(frameIndex + 1) / Double(totalFrames) * 100
            progressHandler?(progress) // 调用进度回调
        }
        
        // 处理完成后创建可编辑段落 + 文本清理优化
        var newSegments: [EditableSegment] = []
        for (text, segmentInfo) in currentSegments {
            let baseText = text.split(separator: "_").first.map(String.init) ?? text

            // 🔧 应用文本清理：去除水印
            let cleanedText = cleanSubtitleText(baseText)

            // 跳过空文本或只有水印的文本
            if cleanedText.isEmpty {
                continue
            }

            // 🔧 格式化中英文分行显示
            var formattedText = cleanedText
            if let chineseText = segmentInfo.chineseText, let englishText = segmentInfo.englishText,
               !chineseText.isEmpty, !englishText.isEmpty {
                // 清理中英文文本
                let cleanChinese = cleanSubtitleText(chineseText)
                let cleanEnglish = cleanSubtitleText(englishText)
                if !cleanChinese.isEmpty && !cleanEnglish.isEmpty {
                    formattedText = "\(cleanChinese)\n\(cleanEnglish)"
                } else if !cleanChinese.isEmpty {
                    formattedText = cleanChinese
                } else if !cleanEnglish.isEmpty {
                    formattedText = cleanEnglish
                } else {
                    continue // 都是水印，跳过
                }
            } else {
                formattedText = formatChineseEnglishSubtitle(cleanedText)
            }

            // 检查是否为CJK语言
            let isCJKLanguage = containsCJKCharacters(formattedText)

            let word = EditableWord(
                word: formattedText,
                start: segmentInfo.startTime,
                end: segmentInfo.frames.last?.timestamp ?? segmentInfo.startTime,
                probability: 1.0
            )

            newSegments.append(EditableSegment(words: [word], isCJKLanguage: isCJKLanguage))
        }
        
        // 按时间排序
        newSegments.sort { $0.startTime < $1.startTime }
        
        await MainActor.run {
            self.subtitleSegments = newSegments
            self.isProcessing = false
            print("字幕处理完成，共检测到 \(newSegments.count) 个字幕段落")
        }
        
        return newSegments
    }

    // 裁剪图像到指定区域
    private func cropImage(_ image: NSImage, to rect: CGRect) -> NSImage? {
        guard let cgImage = image.cgImage(forProposedRect: nil, context: nil, hints: nil) else {
            return nil
        }
        
        let imageSize = image.size
        
        // 将标准化坐标转换为像素坐标
        let x = rect.origin.x * imageSize.width
        let y = rect.origin.y * imageSize.height
        let width = rect.width * imageSize.width
        let height = rect.height * imageSize.height
        
        // 创建裁剪区域
        let cropRect = CGRect(x: x, y: y, width: width, height: height)
        
        // 裁剪图像
        guard let croppedCGImage = cgImage.cropping(to: cropRect) else {
            return nil
        }
        
        return NSImage(cgImage: croppedCGImage, size: NSSize(width: width, height: height))
    }

    // 创建字幕段落
    private func createSubtitleSegment(text: String, startTime: Double, endTime: Double) -> EditableSegment {
        // 检查是否为CJK语言
        let isCJKLanguage = containsCJKCharacters(text)
        
        let word = EditableWord(
            word: text,
            start: startTime,
            end: endTime,
            probability: 1.0
        )
        
        return EditableSegment(words: [word], isCJKLanguage: isCJKLanguage)
    }

    // 判断文本是否包含CJK字符
    private func containsCJKCharacters(_ text: String) -> Bool {
        let pattern = "\\p{Han}|\\p{Hiragana}|\\p{Katakana}|\\p{Hangul}"
        let regex = try? NSRegularExpression(pattern: pattern, options: [])
        let range = NSRange(location: 0, length: text.utf16.count)
        return regex?.firstMatch(in: text, options: [], range: range) != nil
    }

    // 分离中文和英文
    private func separateChineseAndEnglish(_ text: String) -> (String?, String?) {
        var chineseText: String? = nil
        var englishText: String? = nil
        
        // 中文字符正则表达式
        let chinesePattern = "[\\p{Han}\\p{InCJK_Symbols_and_Punctuation}]+"
        let chineseRegex = try? NSRegularExpression(pattern: chinesePattern, options: [])
        
        // 英文字符正则表达式 (包括标点符号和数字)
        let englishPattern = "[A-Za-z0-9\\s.,?!'\";:\\-()\\[\\]{}]+"
        let englishRegex = try? NSRegularExpression(pattern: englishPattern, options: [])
        
        // 提取中文
        if let regex = chineseRegex {
            let range = NSRange(location: 0, length: text.utf16.count)
            let matches = regex.matches(in: text, options: [], range: range)
            
            if !matches.isEmpty {
                var chineseChars = [String]()
                for match in matches {
                    if let range = Range(match.range, in: text) {
                        chineseChars.append(String(text[range]))
                    }
                }
                chineseText = chineseChars.joined(separator: "")
            }
        }
        
        // 提取英文
        if let regex = englishRegex {
            let range = NSRange(location: 0, length: text.utf16.count)
            let matches = regex.matches(in: text, options: [], range: range)
            
            if !matches.isEmpty {
                var englishWords = [String]()
                for match in matches {
                    if let range = Range(match.range, in: text) {
                        englishWords.append(String(text[range]))
                    }
                }
                englishText = englishWords.joined(separator: " ").trimmingCharacters(in: .whitespacesAndNewlines)
            }
        }
        
        return (chineseText, englishText)
    }

    // 更新文本段落，添加中英文分离支持
    private func updateTextSegments(
        frame: DetectedFrame, 
        chineseText: String?, 
        englishText: String?,
        currentSegments: inout [String: (startTime: Double, frames: [DetectedFrame], textBounds: CGRect, confidence: VNConfidence, chineseText: String?, englishText: String?)]
    ) {
        guard !frame.detectedText.isEmpty else { return }
        
        // 清理和标准化检测到的文本
        let cleanedText = frame.detectedText.trimmingCharacters(in: .whitespacesAndNewlines)
        
        // 检查是否是相同或相似的文本
        if let existingSegment = currentSegments.first(where: { key, value in
            // 使用更宽松的匹配条件，允许小的差异
            let existingText = String(key.split(separator: "_").first ?? "") // 转换为 String
            let similarity = calculateSimilarity(between: existingText, and: cleanedText)
            return similarity > 0.8 // 80% 相似度阈值
        })?.key {
            // 获取现有段落的信息
            if let existingValue = currentSegments[existingSegment] {
                if frame.timestamp - existingValue.frames.last!.timestamp < 2.0 {
                    // 创建更新后的值
                    var updatedValue = existingValue
                    updatedValue.frames.append(frame)
                    updatedValue.textBounds = existingValue.textBounds.union(frame.textBounds)
                    updatedValue.confidence = max(existingValue.confidence, frame.confidence)
                    
                    // 更新中英文文本（如果有新的）
                    if let newChineseText = chineseText, !newChineseText.isEmpty {
                        updatedValue.chineseText = newChineseText
                    }
                    if let newEnglishText = englishText, !newEnglishText.isEmpty {
                        updatedValue.englishText = newEnglishText
                    }
                    
                    // 更新字典
                    currentSegments[existingSegment] = updatedValue
                } else {
                    // 如果时间间隔太大，创建新的段落
                    let newKey = "\(cleanedText)_\(frame.timestamp)"
                    currentSegments[newKey] = (
                        startTime: frame.timestamp,
                        frames: [frame],
                        textBounds: frame.textBounds,
                        confidence: frame.confidence,
                        chineseText: chineseText,
                        englishText: englishText
                    )
                }
            }
        } else {
            // 创建新的段落
            currentSegments[cleanedText] = (
                startTime: frame.timestamp,
                frames: [frame],
                textBounds: frame.textBounds,
                confidence: frame.confidence,
                chineseText: chineseText,
                englishText: englishText
            )
        }
    }

    // 导出字幕为SRT格式
    func exportSubtitlesToSRT() -> String {
        var srtContent = ""
        
        for (index, segment) in subtitleSegments.enumerated() {
            srtContent += "\(index + 1)\n"
            srtContent += "\(formatTimeForSRT(seconds: segment.startTime)) --> \(formatTimeForSRT(seconds: segment.endTime))\n"
            srtContent += "\(segment.text)\n\n"
        }
        
        return srtContent
    }

    // 格式化时间为SRT格式
    private func formatTimeForSRT(seconds: Double) -> String {
        let hours = Int(seconds) / 3600
        let minutes = (Int(seconds) % 3600) / 60
        let secs = Int(seconds) % 60
        let milliseconds = Int((seconds - Double(Int(seconds))) * 1000)
        
        return String(format: "%02d:%02d:%02d,%03d", hours, minutes, secs, milliseconds)
    }

    // MARK: - 🔧 文本清理和过滤

    /// 清理字幕文本，去除水印和干扰信息
    private func cleanSubtitleText(_ text: String) -> String {
        var cleanedText = text.trimmingCharacters(in: .whitespacesAndNewlines)

        // 🔑 去除常见水印和台标
        let watermarkPatterns = [
            "M|W",
            "SDOM",           // 您提到的水印
            "MODERN WISDOM",  // 从截图看到的水印
            "©.*",           // 版权符号及后续内容
            "www\\..*",      // 网址
            "http.*",        // 链接
            "@.*",           // 社交媒体账号
            "字幕组.*",       // 字幕组信息
            ".*字幕组",       // 字幕组信息
            "SUB.*",         // SUB开头的标识
            ".*SUB",         // SUB结尾的标识
            "\\[.*\\]",      // 方括号内容
            "【.*】",         // 中文方括号内容
        ]

        for pattern in watermarkPatterns {
            cleanedText = cleanedText.replacingOccurrences(
                of: pattern,
                with: "",
                options: [.regularExpression, .caseInsensitive]
            )
        }

        // 清理多余的空白字符
        cleanedText = cleanedText.replacingOccurrences(
            of: "\\s+",
            with: " ",
            options: .regularExpression
        ).trimmingCharacters(in: .whitespacesAndNewlines)

        return cleanedText
    }

    /// 格式化中英文字幕为分行显示
    private func formatChineseEnglishSubtitle(_ text: String) -> String {
        let (chineseText, englishText) = separateChineseAndEnglish(text)

        // 如果同时有中文和英文，分行显示
        if let chinese = chineseText, let english = englishText,
           !chinese.isEmpty, !english.isEmpty {
            return "\(chinese)\n\(english)"
        }

        // 只有中文或只有英文，直接返回
        return text
    }

    // 🔑 高效字幕区域处理 - 与processVideo性能相同
    func processSubtitleRegionFast(
        url: URL,
        region: CGRect,
        recognitionLevel: VNRequestTextRecognitionLevel = .accurate,
        progressHandler: ((Double) -> Void)? = nil
    ) async throws -> [EditableSegment] {

        await MainActor.run {
            self.isProcessing = true
            self.progress = 0
        }

        var subtitleSegments: [EditableSegment] = []
        var currentSegments: [String: (startTime: Double, frames: [DetectedFrame], textBounds: CGRect, confidence: VNConfidence)] = [:]

        let asset = AVAsset(url: url)
        let duration = try await asset.load(.duration)
        let durationSeconds = CMTimeGetSeconds(duration)

        // 🔑 与processVideo完全相同的采样策略
        let frameRate: Double = 2
        let totalFrames = Int(durationSeconds * frameRate)

        let generator = AVAssetImageGenerator(asset: asset)
        generator.appliesPreferredTrackTransform = true
        generator.requestedTimeToleranceBefore = .zero
        generator.requestedTimeToleranceAfter = .zero

        // 🔑 预计算裁剪区域的绝对坐标
        var absoluteRegion: CGRect = .zero
        var regionCalculated = false

        // 🔑 关键帧检测变量
        var lastFrameHash: String = ""
        var consecutiveSameFrames: Int = 0

        for frameIndex in 0..<totalFrames {
            let timeInSeconds = Double(frameIndex) / frameRate
            let time = CMTime(seconds: timeInSeconds, preferredTimescale: 600)
            
            do {
                let cgImage = try generator.copyCGImage(at: time, actualTime: nil)
                
                // 🔑 只在第一帧计算绝对区域坐标
                if !regionCalculated {
                    let imageWidth = CGFloat(cgImage.width)
                    let imageHeight = CGFloat(cgImage.height)
                    absoluteRegion = CGRect(
                        x: region.origin.x * imageWidth,
                        y: region.origin.y * imageHeight,
                        width: region.width * imageWidth,
                        height: region.height * imageHeight
                    )
                    regionCalculated = true
                }
                
                // 🔑 直接在CGImage级别裁剪，避免NSImage转换开销
                guard let croppedCGImage = cgImage.cropping(to: absoluteRegion) else {
                    continue
                }

                // 🔑 关键帧检测 - 跳过相似帧
                let currentFrameHash = quickImageHash(croppedCGImage)
                if currentFrameHash == lastFrameHash {
                    consecutiveSameFrames += 1
                    if consecutiveSameFrames >= 3 { // 连续3帧相同则跳过
                        continue
                    }
                } else {
                    consecutiveSameFrames = 0
                }
                lastFrameHash = currentFrameHash

                let nsImage = NSImage(cgImage: croppedCGImage, size: NSSize(width: croppedCGImage.width, height: croppedCGImage.height))

                // 🔑 使用与processVideo相同的文本检测逻辑
                if let (detectedText, textBounds, confidence) = try await detectText(in: nsImage, recognitionLevel: recognitionLevel) {
                    let frame = DetectedFrame(
                        image: nsImage,
                        detectedText: detectedText,
                        timestamp: timeInSeconds,
                        textBounds: textBounds,
                        confidence: confidence
                    )
                    
                    updateTextSegments(frame: frame, currentSegments: &currentSegments)
                }
            } catch {
                print("处理第 \(frameIndex) 帧时出错: \(error)")
                continue
            }
            
            // 更新进度
            let progress = Double(frameIndex + 1) / Double(totalFrames) * 100
            progressHandler?(progress)
        }
        
        // 🔑 与processVideo相同的段落处理逻辑 + 文本清理优化
        var newSegments: [EditableSegment] = []
        for (text, segmentInfo) in currentSegments {
            let baseText = text.split(separator: "_").first.map(String.init) ?? text

            // 🔧 应用文本清理：去除水印
            let cleanedText = cleanSubtitleText(baseText)

            // 跳过空文本或只有水印的文本
            if cleanedText.isEmpty {
                continue
            }

            // 🔧 格式化中英文分行显示
            let formattedText = formatChineseEnglishSubtitle(cleanedText)

            let isCJKLanguage = containsCJKCharacters(formattedText)

            let word = EditableWord(
                word: formattedText,
                start: segmentInfo.startTime,
                end: segmentInfo.frames.last?.timestamp ?? segmentInfo.startTime,
                probability: 1.0
            )

            newSegments.append(EditableSegment(words: [word], isCJKLanguage: isCJKLanguage))
        }
        
        // 按时间排序
        newSegments.sort { $0.startTime < $1.startTime }
        
        await MainActor.run {
            self.subtitleSegments = newSegments
            self.isProcessing = false
            print("字幕处理完成，共检测到 \(newSegments.count) 个字幕段落")
        }
        
        return newSegments
    }

    // MARK: - 🔑 关键帧检测优化

    /// 快速图像哈希 - 用于检测相似帧
    private func quickImageHash(_ cgImage: CGImage) -> String {
        // 快速8x8采样哈希
        let width = 8
        let height = 8

        guard let context = CGContext(
            data: nil,
            width: width,
            height: height,
            bitsPerComponent: 8,
            bytesPerRow: width,
            space: CGColorSpaceCreateDeviceGray(),
            bitmapInfo: CGImageAlphaInfo.none.rawValue
        ) else { return "" }

        context.interpolationQuality = .none
        context.draw(cgImage, in: CGRect(x: 0, y: 0, width: width, height: height))

        guard let data = context.data else { return "" }
        let buffer = data.bindMemory(to: UInt8.self, capacity: width * height)

        // 计算平均亮度
        var total = 0
        for i in 0..<(width * height) {
            total += Int(buffer[i])
        }
        let average = total / (width * height)

        // 生成二进制哈希
        var hash = ""
        for i in 0..<(width * height) {
            hash += String(buffer[i] > average ? "1" : "0")
        }

        return hash
    }

    /// 检测是否为关键帧（变化足够大）
    private func isKeyFrame(_ currentHash: String, _ lastHash: String, threshold: Int = 8) -> Bool {
        guard currentHash.count == lastHash.count else { return true }

        var differences = 0
        for (c1, c2) in zip(currentHash, lastHash) {
            if c1 != c2 {
                differences += 1
                if differences > threshold {
                    return true // 变化足够大，是关键帧
                }
            }
        }

        return differences > threshold
    }
}

struct DetectedFrame: Identifiable {
    let id = UUID()
    let image: NSImage
    let detectedText: String
    let timestamp: Double
    let textBounds: CGRect
    let confidence: VNConfidence
}
