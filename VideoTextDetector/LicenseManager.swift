//
//  LicenseManager.swift
//  Moca
//
//  Created by lhr on 6/6/25.
//

import Foundation
import StoreKit

enum LicenseType {
    case free
    case pro // 包括年费专业版
    case lifetime // 终身会员
}

// 专业版功能枚举
enum ProFeature: String, CaseIterable {
    case highQualityModel = "high_quality_model"
    case aiOptimization = "ai_optimization"
    case aiTranslation = "ai_translation"
    case prioritySupport = "priority_support"
    case earlyAccess = "early_access"
    case lifetimeUpdates = "lifetime_updates"
    
    var displayName: String {
        switch self {
        case .highQualityModel:
            return String(localized: "高质量转录模型")
        case .aiOptimization:
            return String(localized: "AI断句优化")
        case .aiTranslation:
            return String(localized: "AI翻译优化")
        case .prioritySupport:
            return String(localized: "优先技术支持")
        case .earlyAccess:
            return String(localized: "抢先体验新功能")
        case .lifetimeUpdates:
            return String(localized: "终身免费更新")
        }
    }
    
    var description: String {
        switch self {
        case .highQualityModel:
            return String(localized: "使用更准确的转录模型（Large系列）")
        case .aiOptimization:
            return String(localized: "智能优化字幕断句")
        case .aiTranslation:
            return String(localized: "智能优化翻译质量")
        case .prioritySupport:
            return String(localized: "获得优先技术支持")
        case .earlyAccess:
            return String(localized: "抢先体验新功能")
        case .lifetimeUpdates:
            return String(localized: "获得所有未来更新")
        }
    }
}

class LicenseManager: ObservableObject {
    static let shared = LicenseManager()
    private var currentProTransaction: Transaction?
    var currentProPayload: [String: Any]? // 新增属性

    @Published var currentLicenseType: LicenseType = .free

    // UserDefaults Key for license state
    private let licenseStateKey = "mocaSubtitleLicenseState"

    init() {
        loadLicenseState()
        
        // 监听 StoreKit 交易更新，确保许可证状态同步
        Task {
            await listenForTransactions()
        }
    }

    // 监听交易更新
    private func listenForTransactions() async {
        for await result in Transaction.updates {
            do {
                let transaction = try StoreManager.shared.checkVerified(result) // 使用 StoreManager 的 checkVerified

                // 根据购买的产品更新许可证状态
                await updateLicenseStatusAll()
                await transaction.finish()
            } catch {
                print("交易验证失败: \(error)")
            }
        }
    }

    // 更新许可证状态
    @MainActor
    func updateLicenseStatusAll() async {
        // 重置 payload
        self.currentProPayload = nil

        if let lifetimeTransaction = await StoreManager.shared.getLatestTransaction(for: StoreManager.ProductID.lifetimeVersion.rawValue),
        lifetimeTransaction.productType == .nonConsumable {
            currentLicenseType = .lifetime
        } else if let proTransaction = await StoreManager.shared.getLatestTransaction(for: StoreManager.ProductID.proVersion.rawValue),
                proTransaction.productType == .autoRenewable,
                (proTransaction.expirationDate ?? Date.distantPast) > Date() {
            // print("[Debug] 找到Pro版本交易，产品ID: \(proTransaction.productID)，过期时间: \(proTransaction.expirationDate ?? Date.distantPast)")
            currentLicenseType = .pro
            // 直接解析JSON payload，而不是JWS
            // do {
            //     let json = try JSONSerialization.jsonObject(with: proTransaction.jsonRepresentation, options: [])
            //     self.currentProPayload = json as? [String: Any]
            //     print("[Debug] 解码后的 Payload: \(self.currentProPayload ?? [:])")
            // } catch {
            //     print("[Debug] JSON Payload解码失败: \(error)")
            //     self.currentProPayload = nil
            // }

        } else {
            currentLicenseType = .free
        }
        saveLicenseState()
    }

    // 在购买成功时调用，确保状态更新
    func purchaseCompleted(productID: String) {
        Task { @MainActor in
            await updateLicenseStatusAll()
        }
    }

    // 在恢复购买成功时调用，确保状态更新
    func restoreCompleted() {
        Task { @MainActor in
            // 重新检查所有已购产品，更新状态
            await StoreManager.shared.loadPurchasedProducts() // 确保 StoreManager 重新加载已购产品
            for productID in StoreManager.shared.purchasedProductIDs {
                await updateLicenseStatusAll()
            }
        }
    }

    // 辅助函数：检查是否是专业版（包括年费和终身）
    func isPro() -> Bool {
        return currentLicenseType == .pro || currentLicenseType == .lifetime
    }

    // 辅助函数：检查是否是终身会员
    func isLifetime() -> Bool {
        return currentLicenseType == .lifetime
    }

    // 恢复购买
    func restorePurchases() async {
        do {
            // 请求最新的交易信息
            try await AppStore.sync()
            
            // 检查当前授权
            for await transactionResult in Transaction.currentEntitlements {
                if case .verified(let transaction) = transactionResult {
                    await updateLicenseStatusAll()
                }
            }
        } catch {
            print("恢复购买失败: \(error)")
        }
    }

    // MARK: - 许可证状态持久化
    private func saveLicenseState() {
        UserDefaults.standard.set(currentLicenseType.rawValue, forKey: licenseStateKey)
    }

    private func loadLicenseState() {
        if let savedTypeString = UserDefaults.standard.string(forKey: licenseStateKey),
           let savedType = LicenseType(rawValue: savedTypeString) {
            currentLicenseType = savedType
        } else {
            currentLicenseType = .free
        }
    }
    
    // !!! 仅用于开发调试，发布前请删除或禁用此方法 !!!
    func resetLicenseStateForDebugging() {
        UserDefaults.standard.removeObject(forKey: licenseStateKey)
        currentLicenseType = .free
        print("许可证状态已重置为免费版。")
    }
}

// 为 LicenseType 添加 RawRepresentable 协议，以便 UserDefaults 存储
extension LicenseType: RawRepresentable {
    typealias RawValue = String

    init?(rawValue: String) {
        switch rawValue {
        case "free": self = .free
        case "pro": self = .pro
        case "lifetime": self = .lifetime
        default: return nil
        }
    }

    var rawValue: String {
        switch self {
        case .free: return "free"
        case .pro: return "pro"
        case .lifetime: return "lifetime"
        }
    }
}
