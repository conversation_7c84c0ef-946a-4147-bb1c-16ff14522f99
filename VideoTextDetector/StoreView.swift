//
//  StoreView.swift
//  VideoTextDetector
//
//  Created by lhr on 5/28/25.
//

import SwiftUI
import StoreKit

struct PlanCard: View {
    let title: String
    let price: String
    let originalPrice: String?
    let features: [String]
    let isCurrentPlan: Bool
    let action: (() -> Void)?
    let extraInfo: String?
    
    var body: some View {
        VStack(spacing: 16) {
            // 标题
            Text(title)
                .font(.title3)
                .fontWeight(.bold)
            
            // 价格显示
            HStack(alignment: .bottom, spacing: 5) {
                Text(price)
                    .font(.title)
                    .fontWeight(.bold)
                    .foregroundColor(price == "免费" ? .gray : .blue)
                
                if let original = originalPrice, price != "免费" {
                    Text(original)
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .strikethrough(true, color: .secondary)
                        .offset(y: -2)
                }
            }
            
            // 添加限时优惠信息
            if title == "专业版" || title == "终身会员版" {
                Text("早期用户专享")
                    .font(.caption)
                    .fontWeight(.semibold)
                    .foregroundColor(.red) // 使用红色突出显示
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(Capsule().fill(Color.red.opacity(0.1))) // 胶囊背景
            }
            
            Divider()
            
            // 功能列表
            VStack(alignment: .leading, spacing: 12) {
                ForEach(features, id: \.self) { feature in
                    HStack(alignment: .top, spacing: 8) {
                        Image(systemName: "checkmark.circle.fill")
                            .foregroundColor(.green)
                            .font(.system(size: 14))
                        
                        Text(feature)
                            .font(.subheadline)
                            .multilineTextAlignment(.leading)
                    }
                }
            }
            .frame(maxWidth: .infinity, alignment: .leading)
            
            Spacer()
            
            // 购买按钮
            if let action = action {
                Button(action: action) {
                    if isCurrentPlan {
                        Text("当前版本")
                            .font(.headline)
                            .frame(maxWidth: .infinity)
                            .padding(.vertical, 12)
                            .foregroundColor(.gray)
                            .background(Color.gray.opacity(0.1))
                            .cornerRadius(8)
                    } else if let extraInfo = extraInfo {
                        Text(extraInfo)
                            .font(.caption)
                            .foregroundColor(.orange)
                            .frame(maxWidth: .infinity)
                            .padding(.vertical, 12)
                    } else {
                        Text("立即购买")
                            .font(.headline)
                            .frame(maxWidth: .infinity)
                            .padding(.vertical, 12)
                    }
                }
                .buttonStyle(.borderedProminent)
                .disabled(isCurrentPlan)
            }
        }
        .padding(20)
        .frame(maxWidth: .infinity)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .stroke(Color.gray.opacity(0.2), lineWidth: 1)
                .background(Color(.windowBackgroundColor))
        )
    }
}

struct StoreView: View {
    var onClose: (() -> Void)? = nil
    @Environment(\.dismiss) private var dismiss
    @StateObject private var storeManager = StoreManager.shared
    @StateObject private var licenseManager = LicenseManager.shared
    @State private var isPurchasing = false
    @State private var showAlert = false
    @State private var alertTitle = ""
    @State private var alertMessage = ""
    
    var body: some View {
        VStack(spacing: 0) {
            // 标题栏
            HStack {
                // 标题
                HStack {
                    Image(systemName: "cart.fill")
                        .font(.title2)
                    Text("选择您的计划")
                        .font(.title2)
                        .fontWeight(.bold)
                }
                
                Spacer()
                
                // 关闭按钮
                Button(action: {
                    if let onClose = onClose {
                        onClose()
                    } else {
                        dismiss()
                    }
                }) {
                    Image(systemName: "xmark.circle.fill")
                        .font(.title2)
                        .foregroundColor(.gray)
                }
                .buttonStyle(PlainButtonStyle())
                .focusEffectDisabled()
                .onTapGesture {
                    if let onClose = onClose {
                        onClose()
                    } else {
                        dismiss()
                    }
                }
            }
            .padding(.horizontal, 20)
            .padding(.vertical, 12)
            
            Divider()
            
            // 主要内容
            ScrollView {
                VStack(spacing: 20) {
                    // 订阅信息说明
                    // VStack(alignment: .leading, spacing: 8) {
                    //     Text("订阅信息")
                    //         .font(.headline)
                    //         .fontWeight(.bold)
                        
                    //     VStack(alignment: .leading, spacing: 4) {
                    //         Text("• 专业版：年费订阅，自动续费")
                    //         Text("• 订阅时长：12个月")
                    //         Text("• 价格：\(storeManager.product(for: .proVersion)?.displayPrice ?? "¥48")/年")
                    //         Text("• 终身版：一次性购买，永久有效")
                    //         Text("• 价格：\(storeManager.product(for: .lifetimeVersion)?.displayPrice ?? "¥98")")
                    //     }
                    //     .font(.caption)
                    //     .foregroundColor(.secondary)
                    // }
                    // .padding()
                    // .background(Color.gray.opacity(0.1))
                    // .cornerRadius(8)
                    
                    // 三列布局
                    HStack(spacing: 20) {
                        // 免费版
                        PlanCard(
                            title: String(localized: "免费版"),
                            price: String(localized:  "免费"),
                            originalPrice: nil,
                            features: [
                                "基础转录模型无限转录",
                                "基础字幕操作：合并、分割、编辑",
                                "导出SRT格式字幕",
                                "基础翻译功能",
                                "视频文字识别",
                                "本地缓存3天自动删除"
                            ].map { String(localized: $0) },
                            isCurrentPlan: licenseManager.currentLicenseType == .free,
                            action: nil,
                            extraInfo: nil
                        )
                        
                        // 专业版 (年会员)
                        PlanCard(
                            title: String(localized: "专业版"),
                            price: (storeManager.product(for: .proVersion)?.displayPrice).map { "\($0)/\(String(localized: "年"))" } ?? "¥48/\(String(localized: "年"))",
                            originalPrice: "¥198/\(String(localized: "年"))",
                            features: [
                                "包含所有免费版功能",
                                "高质量转录模型",
                                "AI断句优化",
                                "AI翻译优化",
                                "一年内免费更新",
                                "含14天免费试用"
                            ].map { String(localized: $0) },
                            isCurrentPlan: licenseManager.currentLicenseType == .pro,
                            action: (licenseManager.currentLicenseType == .pro) ? nil : (
                                (licenseManager.currentLicenseType == .lifetime) ? nil : {
                                    Task { await purchaseProduct(.proVersion) }
                                }
                            ),
                            extraInfo: (licenseManager.currentLicenseType == .lifetime) ? "已包含在终身会员中" : nil
                        )
                        
                        // 终身会员版
                        PlanCard(
                            title: String(localized: "终身会员版"),
                            price: storeManager.product(for: .lifetimeVersion)?.displayPrice ?? "¥98",
                            originalPrice: "¥398",
                            features: [
                                "包含所有专业版功能",
                                "优先技术支持",
                                "抢先体验新功能",
                                "一次付费永久有效",
                                "终身免费更新"
                            ].map { String(localized: $0) },
                            isCurrentPlan: licenseManager.currentLicenseType == .lifetime,
                            action: licenseManager.currentLicenseType == .lifetime ? nil : {
                                Task { await purchaseProduct(.lifetimeVersion) }
                            },
                            extraInfo: nil
                        )
                    }
                    .padding(.vertical, 1)

                    HStack(spacing: 0) {
                        Button(action: {
                            if let url = URL(string: "https://nickel-increase-8fb.notion.site/Privacy-Policy-21182861906380518b22ceb415e35711") {
                                NSWorkspace.shared.open(url)
                            }
                        }) {
                            Text("隐私政策")
                                .underline()
                        }
                        .buttonStyle(PlainButtonStyle())
                        .font(.system(size: 12))
                        .padding(.horizontal, 8)

                       // 蓝色分隔符
                        Text("｜")
                            .foregroundColor(.accentColor)
                            .font(.system(size: 12))

                        Button(action: {
                            if let url = URL(string: "https://nickel-increase-8fb.notion.site/Terms-of-Use-21182861906380b2b82bfb9459399273") {
                                NSWorkspace.shared.open(url)
                            }
                        }) {
                            Text("使用条款")
                                .underline()
                        }
                        .buttonStyle(PlainButtonStyle())
                        .font(.system(size: 12))
                        .padding(.horizontal, 8)
                    }
                    .padding(.vertical, 8)
                    
                    // 恢复购买按钮
                    Button("恢复购买") {
                        Task {
                            await restorePurchases()
                        }
                    }
                    .buttonStyle(.borderless)
                    .disabled(isPurchasing)
                    .padding(.bottom, 10)
                    
                    // 订阅管理信息
                    // VStack(alignment: .leading, spacing: 4) {
                    //     Text("订阅管理")
                    //         .font(.caption)
                    //         .fontWeight(.semibold)
                        
                    //     Text("• 订阅将在当前期间结束前24小时内自动续费")
                    //     Text("• 您可以在购买后随时在App Store设置中取消订阅")
                    //     Text("• 取消后，您仍可使用到当前订阅期结束")
                    // }
                    // .font(.caption2)
                    // .foregroundColor(.secondary)
                    // .padding(.horizontal, 20)
                    // .padding(.bottom, 20)
                }
                .padding(.horizontal, 2)
            }
            // Text("产品数量：\(storeManager.products.count)")
            // Text("StoreKit产品数量：\(storeManager.products.count)")
            // if storeManager.isLoading {
            //     Text("正在加载产品信息...")
            //         .foregroundColor(.gray)
            // }
            // if let error = storeManager.error {
            //     Text("StoreKit错误: \(error)")
            //         .foregroundColor(.red)
            // }
        }
        .background(Color(.windowBackgroundColor))
        .task {
            await storeManager.loadProducts()
            // 只需要检查一次，或者遍历所有 productID，找到有效的那一个
            // await LicenseManager.shared.updateLicenseStatusAll()
        }
        .alert(alertTitle, isPresented: $showAlert) {
            Button("确定", role: .cancel) { }
        } message: {
            Text(alertMessage)
        }
    }
    
    private func purchaseProduct(_ productID: StoreManager.ProductID) async {
        guard let product = storeManager.product(for: productID) else {
            alertTitle = "错误"
            alertMessage = "无法找到产品信息。请稍后再试。"
            showAlert = true
            return
        }

        isPurchasing = true
        
        // 添加调试信息
        // print("=== 购买流程调试信息 ===")
        // print("开始购买产品: \(product.id)")
        // print("产品价格: \(product.displayPrice)")
        // print("产品类型: \(product.type)")
        // print("产品标题: \(product.displayName)")
        // print("产品描述: \(product.description)")
        
        do {
            let result = try await product.purchase()
            // print("购买结果: \(result)")
            
            switch result {
            case .success(let verification):
                // print("✅ 购买成功，开始验证...")
                let transaction = try storeManager.checkVerified(verification)
                let data: Data? = transaction.jsonRepresentation
                if let data = data,
                    let json = try? JSONSerialization.jsonObject(with: data) as? [String: Any],
                    let payload = json["payload"] as? [String: Any] {
                        LicenseManager.shared.currentProPayload = payload
                    }
                // print("✅ 交易验证成功: \(transaction.productID)")
                // print("   交易ID: \(transaction.id)")
                // print("   交易类型: \(transaction.productType)")
                if let expirationDate = transaction.expirationDate {
                    print("   过期时间: \(expirationDate)")
                }

                licenseManager.purchaseCompleted(productID: productID.rawValue)

                alertTitle = "购买成功"
                alertMessage = "您已成功解锁所有专业功能！"
                showAlert = true
                await transaction.finish()
                // print("✅ 交易已完成")
                
            case .pending:
                // print("⏳ 购买待处理")
                alertTitle = "购买待处理"
                alertMessage = "您的购买正在等待批准，请检查您的App Store帐户。"
                showAlert = true
                
            case .userCancelled:
                print("❌ 用户取消购买")
                // 不显示错误，用户主动取消
                
            default:
                // print("❌ 购买失败，未知状态")
                alertTitle = "购买失败"
                alertMessage = "购买失败。请稍后再试。"
                showAlert = true
            }
        } catch {
            // print("❌ 购买过程中发生错误: \(error)")
            // print("错误类型: \(type(of: error))")
            
            // 提供更具体的错误信息
            var errorMessage = "购买过程中发生错误"
            
            if let storeError = error as? StoreKitError {
                switch storeError {
                case .networkError:
                    errorMessage = "网络连接错误，请检查网络连接后重试"
                case .notAvailableInStorefront:
                    errorMessage = "此产品在当前地区不可用"
                case .notEntitled:
                    errorMessage = "您没有购买此产品的权限"
                case .systemError:
                    errorMessage = "系统错误，请稍后重试"
                default:
                    errorMessage = "购买失败: \(error.localizedDescription)"
                }
            } else {
                errorMessage = "购买失败: \(error.localizedDescription)"
            }
            
            alertTitle = "购买失败"
            alertMessage = errorMessage
            showAlert = true
        }
        // print("=== 购买流程调试信息结束 ===")
        isPurchasing = false
    }
    
    private func restorePurchases() async {
        isPurchasing = true
        do {
            try await AppStore.sync()
            
            var hasActivePurchase = false
            for await transactionResult in Transaction.currentEntitlements {
                if case .verified(let transaction) = transactionResult {
                    await licenseManager.updateLicenseStatusAll()
                    
                    if licenseManager.currentLicenseType == .pro || licenseManager.currentLicenseType == .lifetime {
                        hasActivePurchase = true
                    }
                }
            }

            if hasActivePurchase {
                alertTitle = "恢复购买成功"
                alertMessage = "您的专业功能已恢复！"
            } else {
                alertTitle = "未找到购买记录"
                alertMessage = "未找到与此帐户关联的专业版购买记录。"
            }
            showAlert = true
        } catch {
            alertTitle = "恢复购买失败"
            alertMessage = "恢复购买过程中发生错误: \(error.localizedDescription)"
            showAlert = true
        }
        isPurchasing = false
    }
}
