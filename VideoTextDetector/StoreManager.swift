//
//  StoreManager.swift
//  VideoTextDetector
//
//  Created by lhr on 5/28/25.
//

import Foundation
import StoreKit

class StoreManager: NSObject, ObservableObject {
    static let shared = StoreManager()
    
    // 定义产品ID
    enum ProductID: String, CaseIterable {
        case proVersion = "top.emmaflow.MocaSubtitle.discount.proyearly"
        case lifetimeVersion = "top.emmaflow.MocaSubtitle.discount.lifetime"
    }
    
    // 发布可观察对象
    @Published var products: [Product] = []
    @Published var purchasedProductIDs = Set<String>()
    @Published var isLoading = false
    @Published var error: String? = nil
    
    // 用于跟踪交易更新的任务
    private var updateListenerTask: Task<Void, Error>?
    
    override init() {
        super.init()
        // 在这里不再立即请求产品，StoreView的.task会触发
        // updateListenerTask = listenForTransactions() // 确保这行在 init 里
        // loadPurchasedProducts() // 确保这行在 init 里
        // Task { await requestProducts() } // 确保这行在 init 里
    }
    
    deinit {
        updateListenerTask?.cancel()
    }
    
    // 监听交易更新
    internal func listenForTransactions() -> Task<Void, Error> {
        return Task.detached {
            // 持续监听交易队列中的交易更新
            for await result in Transaction.updates {
                do {
                    let transaction = try self.checkVerified(result)
                    
                    // 处理交易
                    await self.updatePurchasedProducts(with: transaction)
                    
                    // 完成交易
                    await transaction.finish()
                } catch {
                    // 处理验证错误
                    print("交易验证失败: \(error)")
                }
            }
        }
    }
    
    // 请求产品信息
    @MainActor
    func requestProducts() async {
        isLoading = true
        error = nil
        
        // print("=== StoreKit 调试信息 ===")
        // print("开始请求产品信息...")
        // print("请求的产品ID: \(ProductID.allCases.map { $0.rawValue })")
        
        // // 添加 Bundle ID 信息
        // if let bundleID = Bundle.main.bundleIdentifier {
        //     print("应用 Bundle ID: \(bundleID)")
        // }
        
        // // 添加沙盒环境信息
        // #if DEBUG
        // print("当前运行在 DEBUG 环境")
        // #endif
        
        // // 检查沙盒环境
        // if let environment = ProcessInfo.processInfo.environment["APP_STORE_ENVIRONMENT"] {
        //     print("App Store 环境: \(environment)")
        // }
        
        // // 检查是否在沙盒环境
        // if let receiptURL = Bundle.main.appStoreReceiptURL {
        //     print("Receipt URL: \(receiptURL)")
        //     if FileManager.default.fileExists(atPath: receiptURL.path) {
        //         print("✅ 存在沙盒收据文件")
        //     } else {
        //         print("❌ 不存在沙盒收据文件")
        //     }
        // }
        
        // // 检查产品配置
        // print("检查产品配置...")
        // for productID in ProductID.allCases {
        //     print("检查产品: \(productID.rawValue)")
        // }
        
        // // 检查当前授权
        // print("检查当前授权...")
        // for await result in Transaction.currentEntitlements {
        //     if case .verified(let transaction) = result {
        //         print("✅ 找到有效交易: \(transaction.productID)")
        //         print("   交易类型: \(transaction.productType)")
        //         print("   交易状态: \(transaction.revocationDate == nil ? "有效" : "已撤销")")
        //         if let expirationDate = transaction.expirationDate {
        //             print("   过期时间: \(expirationDate)")
        //         }
        //     }
        // }
        
        // do {
        //     print("开始从 App Store 请求产品信息...")
        //     // 从App Store请求产品信息
        //     let storeProducts = try await Product.products(for: ProductID.allCases.map { $0.rawValue })
            
        //     print("✅ 成功获取产品信息")
        //     print("获取到的产品数量: \(storeProducts.count)")
        //     if storeProducts.isEmpty {
        //         print("❌ 警告：未获取到任何产品，请检查：")
        //         print("   1. App Store Connect 中产品是否已创建并批准")
        //         print("   2. 产品 ID 是否正确")
        //         print("   3. 产品是否已关联到正确的应用")
        //         print("   4. 沙盒测试账号是否已登录")
        //         print("   5. 网络连接是否正常")
        //     }
            
        //     for product in storeProducts {
        //         print("产品详情:")
        //         print("   ID: \(product.id)")
        //         print("   类型: \(product.type)")
        //         print("   价格: \(product.displayPrice)")
        //         print("   描述: \(product.description)")
        //         print("   标题: \(product.displayName)")
        //     }
            
        //     // 更新产品列表
        //     self.products = storeProducts
        //     isLoading = false
        // } catch {
        //     print("❌ 加载产品失败，错误详情: \(error)")
        //     print("错误类型: \(type(of: error))")
        //     if let storeError = error as? StoreKitError {
        //         print("StoreKit 错误代码: \(storeError)")
        //         switch storeError {
        //         case .networkError:
        //             print("   网络错误 - 请检查网络连接")
        //         case .notAvailableInStorefront:
        //             print("   产品在当前地区不可用")
        //         case .notEntitled:
        //             print("   没有购买此产品的权限")
        //         case .systemError:
        //             print("   系统错误")
        //         default:
        //             print("   其他 StoreKit 错误")
        //         }
        //     }
        //     self.error = "无法加载产品信息: \(error.localizedDescription)"
        //     isLoading = false
        // }
        // print("=== StoreKit 调试信息结束 ===")
    }
    
    // 购买产品
    @MainActor
    func purchase(_ product: Product) async -> Bool {
        do {
            // 请求购买
            let result = try await product.purchase()
            
            switch result {
            case .success(let verification):
                // 验证购买
                let transaction = try checkVerified(verification)
                
                // 更新购买状态
                await updatePurchasedProducts(with: transaction)
                
                // 完成交易
                await transaction.finish()
                
                // 成功购买
                return true
                
            case .userCancelled:
                // 用户取消购买
                return false
                
            case .pending:
                // 购买等待中（如需要家长批准）
                return false
                
            default:
                // 其他情况
                return false
            }
        } catch {
            // 处理购买错误
            self.error = "购买失败: \(error.localizedDescription)"
            return false
        }
    }
    
    // 恢复购买
    @MainActor
    func restorePurchases() async {
        isLoading = true
        error = nil
        
        do {
            // 请求最新的交易信息
            try await AppStore.sync()
            isLoading = false
        } catch {
            self.error = "恢复购买失败: \(error.localizedDescription)"
            isLoading = false
        }
    }
    
    // 验证交易
    internal func checkVerified<T>(_ result: VerificationResult<T>) throws -> T {
        // 确保交易已经过验证
        switch result {
        case .unverified:
            // 交易未通过验证
            throw StoreError.failedVerification
        case .verified(let safe):
            // 交易已验证
            return safe
        }
    }
    
    // 更新购买状态
    @MainActor
    internal func updatePurchasedProducts(with transaction: Transaction) async {
        // 将产品ID添加到已购买集合中
        purchasedProductIDs.insert(transaction.productID)
        
        // 保存购买信息
        savePurchasedProducts()
    }
    
    // 保存已购买产品
    internal func savePurchasedProducts() {
        UserDefaults.standard.set(Array(purchasedProductIDs), forKey: "PurchasedProducts")
    }
    
    // 加载已购买产品
    internal func loadPurchasedProducts() {
        if let savedProducts = UserDefaults.standard.array(forKey: "PurchasedProducts") as? [String] {
            purchasedProductIDs = Set(savedProducts)
        }
    }
    
    // 更新许可证状态
    // private func updateLicenseStatus(for productID: String) {
    //     if productID == ProductID.proVersion.rawValue {
    //         // 更新为专业版
    //         LicenseManager.shared.currentLicenseType = .pro
    //     } else if productID == ProductID.lifetimeVersion.rawValue {
    //         // 更新为终身版
    //         LicenseManager.shared.currentLicenseType = .lifetime
    //     }
    // }
    
    // 检查是否已购买指定产品
    func isProductPurchased(_ productID: ProductID) -> Bool {
        return purchasedProductIDs.contains(productID.rawValue)
    }
    
    // 检查是否已购买任何专业版本
    func hasPurchasedAnyProVersion() -> Bool {
        return isProductPurchased(.proVersion) || isProductPurchased(.lifetimeVersion)
    }
    
    // 获取产品
    func product(for id: ProductID) -> Product? {
        return products.first(where: { $0.id == id.rawValue })
    }
    
    // 获取格式化价格
    func formattedPrice(for product: Product) -> String {
        return product.displayPrice
    }
    
    // 加载产品信息
    @MainActor
    func loadProducts() async {
        isLoading = true
        error = nil
        
        do {
            // 从App Store请求产品信息
            let storeProducts = try await Product.products(for: ProductID.allCases.map { $0.rawValue })
            
            // 更新产品列表
            self.products = storeProducts
            isLoading = false
            // print("StoreManager: Products loaded: \(products.map { $0.id })")
        } catch {
            self.error = "无法加载产品信息: \(error.localizedDescription)"
            isLoading = false
            // print("StoreManager: Failed to load products: \(error.localizedDescription)")
        }
    }
    
    // 辅助方法：获取某个产品ID的最新有效交易（用于判断订阅状态）
    @MainActor
    internal func getLatestTransaction(for productID: String) async -> Transaction? {
        for await result in Transaction.currentEntitlements {
            if case .verified(let transaction) = result {
                if transaction.productID == productID {
                    return transaction
                }
            }
        }
        return nil
    }
}

// 存储错误枚举
enum StoreError: Error {
    case failedVerification
} 
