<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>CFBundleDisplayName</key>
    <string>MocaSubtitle</string>
    <key>CFBundleIdentifier</key>
    <string>top.emmaflow.MocaSubtitle</string>
    <key>CFBundleShortVersionString</key>
    <string>1.0.0</string>
    <key>CFBundleVersion</key>
    <string>1</string>
    <key>LSMinimumSystemVersion</key>
    <string>15.0</string>
    <key>ITSAppUsesNonExemptEncryption</key>
    <false/>
    <key>com.apple.security.temporary-exception.files.absolute-path.read-write</key>
    <array>
        <string>/opt/homebrew/bin/yt-dlp</string>
    </array>
    <key>LSApplicationCategoryType</key>
    <string>public.app-category.video</string>
    <key>NSAppTransportSecurity</key>
    <dict>
        <key>NSAllowsArbitraryLoads</key>
        <false/>
    </dict>
    <key>CFBundleURLTypes</key>
    <array>
        <dict>
            <key>CFBundleTypeRole</key>
            <string>Viewer</string>
            <key>CFBundleURLName</key>
            <string>com.mocasubtitle.app</string>
            <key>CFBundleURLSchemes</key>
            <array>
                <string>mocasubtitle</string>
            </array>
        </dict>
    </array>
</dict>
</plist>