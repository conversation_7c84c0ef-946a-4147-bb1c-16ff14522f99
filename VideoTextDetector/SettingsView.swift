import SwiftUI

struct SettingsView: View {
    @Environment(\.dismiss) private var dismiss
    @State private var selectedTab: SettingsTab = .apiKey
    @State private var apiKey: String = ""
    @State private var selectedProvider: Config.AIProvider = .deepseek // 默认选择第一个服务商
    @State private var showStoreView = false // 添加内购商店视图状态
    
    enum SettingsTab {
        case apiKey
        case support
        case privacy // 添加隐私政策标签页
        case about // 新增
    }
    
    // 添加公开方法用于切换标签
    func switchToTab(_ tab: SettingsTab) {
        selectedTab = tab
    }
    
    var body: some View {
        VStack(spacing: 0) {
            // 标题栏
            HStack {
                Text("设置")
                    .font(.title2)
                    .fontWeight(.bold)
                
                Spacer()
                
                Button(action: { dismiss() }) {
                    Image(systemName: "xmark.circle.fill")
                        .font(.title2)
                        .foregroundColor(.gray)
                }
                .buttonStyle(PlainButtonStyle())
                .focusEffectDisabled() // 👈 添加这一行来移除蓝色焦点框
            }
            .padding(.horizontal, 20)
            .padding(.vertical, 10)
            
            Divider()
            
            // 主内容区域
            HStack(spacing: 0) {
                // 左侧标签栏 - 修改这部分
                VStack(alignment: .leading, spacing: 2) {  // 减小间距
                    ForEach([
                        (SettingsTab.apiKey, "key.fill", String(localized: "API Key设置")),
                        (SettingsTab.support, "heart.fill", String(localized: "MocaSubtitle 专业版")),
                        (SettingsTab.privacy, "hand.raised.fill", String(localized: "隐私与数据")),
                        (SettingsTab.about, "info.circle.fill", String(localized: "关于MocaSubtitle"))
                    ], id: \.0) { tab, icon, title in
                        Button(action: {
                            if tab == .about {
                                if let url = URL(string: "https://nickel-increase-8fb.notion.site/MocaSubtitle-20882861906380eeafc8cdc3f746dcb6") {
                                    NSWorkspace.shared.open(url)
                                }
                                // 不切换 selectedTab
                            } else {
                                selectedTab = tab
                            }
                        }) {
                            HStack {
                                if tab == .about {
                                    Image("moca")
                                        .resizable()
                                        .aspectRatio(contentMode: .fit)
                                        .frame(width: 16, height: 16)
                                        .padding(4)
                                } else {
                                    Image(systemName: icon)
                                        .frame(width: 20)
                                }
                                Text(title)
                            }
                            .foregroundColor(selectedTab == tab ? .accentColor : .primary)
                            .frame(maxWidth: .infinity, alignment: .leading)
                            .padding(.horizontal, 10)
                            .padding(.vertical, 8)  // 调整垂直内边距
                            .background(selectedTab == tab ? Color.accentColor.opacity(0.1) : Color.clear)
                        }
                        .buttonStyle(PlainButtonStyle())
                    }
                    
                    Spacer()  // 将剩余空间推到底部
                }
                .frame(width: 200)
                .padding(.top, 1)  // 添加一点顶部间距
                .background(Color(.windowBackgroundColor))
                
                // 右侧内容区域
                Group {
                    switch selectedTab {
                    case .apiKey:
                        APIKeySettingsView(apiKey: $apiKey, selectedProvider: $selectedProvider)
                    case .support:
                        SupportView(showStoreView: $showStoreView)
                    case .privacy:
                        PrivacyView()
                    case .about:
                        AboutView()
                    }
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
            }
        }
        .frame(width: 800, height: 520)
        .sheet(isPresented: $showStoreView) {
            StoreView()
                .frame(width: 650, height: 540)
        }
        .onAppear {
            loadConfig()
        }
        .onDisappear {
            saveConfig()
        }
    }
    
    private func loadConfig() {
        apiKey = Config.shared.apiKey ?? ""
        selectedProvider = Config.shared.aiProvider
    }
    
    private func saveConfig() {
        Config.shared.apiKey = apiKey.isEmpty ? nil : apiKey
        Config.shared.aiProvider = selectedProvider
    }
}

// 创建一个通用的设置视图容器
struct SettingsContainer<Content: View>: View {
    let content: Content
    
    init(@ViewBuilder content: () -> Content) {
        self.content = content()
    }
    
    var body: some View {
        ScrollView {
            content
                .padding(20)
                .frame(maxWidth: .infinity, alignment: .leading)
        }
    }
}

// API Key 设置视图
struct APIKeySettingsView: View {
    @Binding var apiKey: String
    @Binding var selectedProvider: Config.AIProvider
    
    var body: some View {
        SettingsContainer {
            VStack(alignment: .leading, spacing: 20) {
                HStack {
                    Image(systemName: "key.fill")
                    Text("API Key设置")
                        .font(.headline)
                }
                
                VStack(alignment: .leading, spacing: 10) {
                    Text("在这里可以设置AI服务商的API Key，用于翻译和优化字幕。")
                        .foregroundColor(.secondary)
                    
                    Picker("AI服务商", selection: $selectedProvider) {
                        ForEach(Config.AIProvider.allCases) { provider in
                            Text(provider.displayName)
                                .tag(provider)
                        }
                    }
                    .pickerStyle(SegmentedPickerStyle())
                    .padding(.vertical, 5)
                    
                    SecureField("输入API Key", text: $apiKey)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                        .disableAutocorrection(true)
                    
                    Text("如何获取API Key?")
                        .font(.caption)
                        .foregroundColor(.blue)
                        .onTapGesture {
                            let url: URL
                            switch selectedProvider {
                            case .deepseek:
                                url = URL(string: "https://platform.deepseek.com/api-usage")!
                            case .volcengine:
                                url = URL(string: "https://www.volcengine.com/docs/82379/1494384")!
                            case .aliyun:
                                url = URL(string: "https://help.aliyun.com/zh/model-studio/get-api-key?spm=a2c4g.11186623.0.i3")!
                            case .huaweiyun:
                                url = URL(string: "https://cloud.siliconflow.cn/models")!
                            case .onerouter:
                                url = URL(string: "https://openrouter.ai")!
                            }
                            NSWorkspace.shared.open(url)
                        }
                }
                
                Divider()
                
                Text("安全提示：API Key 仅存储在本地，不会上传至服务器。")
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                Spacer()
            }
        }
    }
}

// 支持我们视图
struct SupportView: View {
    @Binding var showStoreView: Bool

    var body: some View {
        SettingsContainer {
            VStack(alignment: .leading, spacing: 20) {
                HStack {
                    Image(systemName: "heart.fill")
                    Text("MocaSubtitle 专业版")
                        .font(.headline)
                }

                Text("感谢您使用 MocaSubtitle！您的支持是我们不断改进产品的动力。")
                    .font(.headline)
                    .foregroundColor(.secondary)
                
                Divider()
                
                HStack {
                    Image(systemName: "cart.fill")
                    Text("购买 MocaSubtitle 专业版")
                }
                .font(.headline)
                
                GroupBox{
                    VStack(alignment: .leading, spacing: 10) {
                        Text("当前状态")
                            .font(.headline)
                        
                        HStack {
                            if LicenseManager.shared.isPro() {
                                if LicenseManager.shared.isLifetime() {
                                    Text("终身会员")
                                        .font(.title3)
                                        .fontWeight(.semibold)
                                        .foregroundColor(.red)
                                }else {
                                    Text("专业版")
                                        .font(.title3)
                                        .fontWeight(.semibold)
                                        .foregroundColor(.blue)
                                }
                            } else {
                                Text("免费版")
                                    .font(.title3)
                                    .fontWeight(.semibold)
                                    .foregroundColor(.gray)
                            }
                        }
                        .padding()
                        // .background(Color.gray.opacity(0.1))
                        // .cornerRadius(10)
                        .frame(maxWidth: .infinity, alignment: .leading) // 确保填满宽度并左对齐
                    }
                    .frame(maxWidth: .infinity) // 确保 GroupBox 本身填满宽度
                }
                

                // 功能列表
                VStack(alignment: .leading, spacing: 10) {
                    ForEach(ProFeature.allCases, id: \.self) { feature in
                        HStack {
                            Image(systemName: "checkmark.circle.fill")
                                .foregroundColor(.green)
                            Text(feature.displayName)
                                .font(.caption)
                            Text(feature.description)
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }
                }
                .padding(.vertical)
                
                // 修改内购按钮的动作
                Button(action: {
                    showStoreView = true
                }) {
                    Text("立即购买")
                        .foregroundColor(.white)
                        .padding(.horizontal, 20)
                        .padding(.vertical, 8)
                        .background(Color.blue)
                        .cornerRadius(8)
                }
                .buttonStyle(PlainButtonStyle())
                
                Divider()
                
                Text("意见反馈")
                    .font(.headline)

                Button(action: {
                    // 替换为你的邮箱
                    if let url = URL(string: "mailto:<EMAIL>?subject=Feedback%20of%20MocaSubtitle") {
                        NSWorkspace.shared.open(url)
                    }
                }) {
                    Label("电子邮箱", systemImage: "envelope.fill")
                }
                .buttonStyle(LinkButtonStyle())
                
                // 仅用于开发调试：重置许可证状态按钮
                Button(action: {
                    LicenseManager.shared.resetLicenseStateForDebugging()
                }) {
                    Text("重置许可证状态 (仅调试用)")
                        .foregroundColor(.red)
                        .font(.caption)
                }
                .buttonStyle(PlainButtonStyle())

                Spacer()
            }
        }
    }
}

// 隐私视图
struct PrivacyView: View {
    var body: some View {
        SettingsContainer {
            VStack(alignment: .leading, spacing: 20) {
                HStack {
                    Image(systemName: "lock.shield.fill")
                    Text("隐私与数据")
                        .font(.headline)
                }
                
                Text("MocaSubtitle 非常重视您的隐私")
                    .font(.headline)
                    .foregroundColor(.secondary)

                GroupBox{
                    VStack(alignment: .leading, spacing: 12) {
                        PrivacyItem(
                            title: String(localized: "本地处理"),
                            description: String(localized: "您的视频和音频文件完全在本地处理，不会上传至任何服务器。"),
                            icon: "desktopcomputer"
                        )
                        
                        PrivacyItem(
                            title: String(localized: "API密钥安全"),
                            description: String(localized: "您的API密钥仅存储在本地设备上，用于AI服务调用。"),
                            icon: "key.fill"
                        )
                        
                        PrivacyItem(
                            title: String(localized: "隐私保护"),
                            description: String(localized: "我们不收集您的个人信息，除非您明确同意并主动提供。"),
                            icon: "hand.raised.fill"
                        )
                        
                        PrivacyItem(
                            title: String(localized: "数据缓存"),
                            description: String(localized: "视频和字幕的缓存数据仅存储在本地，便于您下次使用。"),
                            icon: "arrow.triangle.2.circlepath"
                        )
                    }
                    .padding()
                    .frame(maxWidth: .infinity, alignment: .leading) // 确保填满宽度并左对齐
                }
                .frame(maxWidth: .infinity) // 确保 GroupBox 本身填满宽度
                
                Divider()
                
                // 权限使用说明
                Text("应用权限说明")
                    .font(.headline)
                
                GroupBox{
                    VStack(alignment: .leading, spacing: 12) {
                        PermissionItem(
                            title: String(localized: "麦克风"),
                            description: String(localized: "用于视频音频转录功能"),
                            icon: "camera.fill"
                        )
                        
                        PermissionItem(
                            title: String(localized: "文件访问"),
                            description: String(localized: "用于读取和保存您选择的媒体文件和字幕"),
                            icon: "folder.fill"
                        )
                        
                        PermissionItem(
                            title: String(localized: "网络访问"),
                            description: String(localized: "用于AI服务调用和应用内购买验证"),
                            icon: "network"
                        )
                    }
                    .padding()
                    .frame(maxWidth: .infinity, alignment: .leading) // 确保填满宽度并左对齐
                    
                    Spacer()
                }
                .frame(maxWidth: .infinity) // 确保 GroupBox 本身填满宽度

                Divider()

                HStack(spacing: 24) {
                Button(action: {
                    if let url = URL(string: "https://nickel-increase-8fb.notion.site/Privacy-Policy-21182861906380518b22ceb415e35711") {
                        NSWorkspace.shared.open(url)
                    }
                }) {
                    Label("隐私政策", systemImage: "lock.shield.fill")
                }
                .buttonStyle(LinkButtonStyle())

                Button(action: {
                    if let url = URL(string: "https://nickel-increase-8fb.notion.site/Terms-of-Use-21182861906380b2b82bfb9459399273") {
                        NSWorkspace.shared.open(url)
                    }
                }) {
                    Label("使用条款", systemImage: "doc.text.fill")
                }
                .buttonStyle(LinkButtonStyle())
            }
            .padding(.vertical, 12)
            }
        }
    }
}

// 隐私项目组件
struct PrivacyItem: View {
    let title: String
    let description: String
    let icon: String
    
    var body: some View {
        HStack(alignment: .top, spacing: 12) {
            Image(systemName: icon)
                .foregroundColor(.blue)
                .frame(width: 24, height: 24)
            
            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .fontWeight(.medium)
                
                Text(description)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
    }
}

// 权限项目组件
struct PermissionItem: View {
    let title: String
    let description: String
    let icon: String
    
    var body: some View {
        HStack(alignment: .top, spacing: 12) {
            Image(systemName: icon)
                .foregroundColor(.orange)
                .frame(width: 24, height: 24)
            
            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .fontWeight(.medium)
                
                Text(description)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
    }
}

struct AboutView: View {
    var body: some View {
    }
}
