import SwiftUI
import WhisperKit
import AVFoundation
import Vision
import CoreML
import AVKit
import Translation

// MARK: - 专业功能工具类
class ProFeatureHelper {
    static let shared = ProFeatureHelper()

    private init() {}

    /// 判断是否为专业模型
    func isProModel(_ modelName: String) -> Bool {
        return modelName.contains("large")
    }

    /// 显示专业功能提示
    func showProFeatureAlert(closeTranscriptionSettings: (() -> Void)? = nil) {
        // 🔑 使用DispatchQueue.main.async避免在SwiftUI事务中运行模态对话框
        DispatchQueue.main.async {
            let alert = NSAlert()
            alert.messageText = "需要专业版（可试用两周）"
            alert.informativeText = "此功能需要购买专业版或终身会员才能使用。"

            // 添加按钮，注意顺序会影响显示位置
            alert.addButton(withTitle: "购买专业版")  // 主要操作
            alert.addButton(withTitle: "关闭")  // 取消操作

            let response = alert.runModal()

            switch response {
            case .alertFirstButtonReturn:  // 购买专业版
                // 🔑 关闭对话框，关闭转录设置页面，打开购买页面
                self.showPurchasePage(closeTranscriptionSettings: closeTranscriptionSettings)

            case .alertSecondButtonReturn:  // 关闭
                // 不需要额外处理，对话框会自动关闭
                break

            default:
                break
            }
        }
    }

    /// 直接显示购买页面（不显示提示对话框）
    func showPurchasePage(closeTranscriptionSettings: (() -> Void)? = nil) {
        // 🎨 先显示购买页面，创造平滑的视觉过渡
        var storeViewController: NSHostingController<StoreView>? = nil
        storeViewController = NSHostingController(rootView: StoreView(onClose: {
            storeViewController?.dismiss(nil as Any?)
        }))
        storeViewController?.preferredContentSize = NSSize(width: 650, height: 540)
        if let window = NSApp.mainWindow {
            window.contentViewController?.presentAsSheet(storeViewController!)
        }

        // 🎨 延迟关闭转录设置页面，让购买页面先显示
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
            closeTranscriptionSettings?()
        }
    }
}

// 在 ContentView 结构体外部添加枚举定义
enum SubtitleExportType {
    case bilingual   // 双语字幕
    case original    // 原文字幕
    case translation // 翻译字幕
}

// 添加新的缓存结构体
struct SubtitleCache: Codable {
    let segments: [EditableSegment]
    let timestamp: Date
    let mediaPath: String // 用于区分不同的媒体文件
}

// 🔧 界面数据隔离管理器
enum InterfaceType: String, CaseIterable {
    case detection = "detection"      // 视频文本检测界面
    case aiOptimization = "ai"        // AI断句/翻译优化界面
    case extraction = "extraction"    // 硬字幕提取界面
}

// 添加字幕缓存管理器
class SubtitleCacheManager {
    static let shared = SubtitleCacheManager()
    private let cacheDirectory: URL
    private let cacheExpiration: TimeInterval = 24 * 3 * 60 * 60 // 3天缓存过期

    private init() {
        let fileManager = FileManager.default
        cacheDirectory = fileManager.urls(for: .cachesDirectory, in: .userDomainMask)[0]
            .appendingPathComponent("SubtitleCache")

        try? fileManager.createDirectory(at: cacheDirectory,
                                      withIntermediateDirectories: true)
    }

    // 🔧 改进的缓存键生成，支持界面隔离
    private func cacheKey(for mediaURL: URL, interface: InterfaceType) -> String {
        let basePath = mediaURL.path.md5
        return "\(basePath)_\(interface.rawValue)"
    }
    
    // 🔧 支持界面隔离的缓存读取
    func getCachedSubtitles(for mediaURL: URL, interface: InterfaceType = .aiOptimization) -> [EditableSegment]? {
        let key = cacheKey(for: mediaURL, interface: interface)
        let cacheFile = cacheDirectory.appendingPathComponent("\(key).json")

        guard let data = try? Data(contentsOf: cacheFile),
              let cache = try? JSONDecoder().decode(SubtitleCache.self, from: data),
              Date().timeIntervalSince(cache.timestamp) < cacheExpiration,
              cache.mediaPath == mediaURL.path // 确保是同一个媒体文件
        else {
            return nil
        }

        return cache.segments
    }

    // 🔧 支持界面隔离的缓存保存
    func cacheSubtitles(_ segments: [EditableSegment], for mediaURL: URL, interface: InterfaceType = .aiOptimization) {
        let key = cacheKey(for: mediaURL, interface: interface)
        let cacheFile = cacheDirectory.appendingPathComponent("\(key).json")

        // print("缓存文件路径: \(cacheFile.path)") // 添加调试信息

        let cache = SubtitleCache(
            segments: segments,
            timestamp: Date(),
            mediaPath: mediaURL.path
        )

        if let data = try? JSONEncoder().encode(cache) {
            // print("开始缓存字幕")
            try? data.write(to: cacheFile)
            // print("缓存字幕到: \(cacheFile.path)")
        } else {
            print("缓存字幕失败")
        }
    }

    // 🔧 兼容旧版本的方法
    func getCachedSubtitles(for mediaURL: URL) -> [EditableSegment]? {
        return getCachedSubtitles(for: mediaURL, interface: .aiOptimization)
    }

    func cacheSubtitles(_ segments: [EditableSegment], for mediaURL: URL) {
        cacheSubtitles(segments, for: mediaURL, interface: .aiOptimization)
    }
    
    // 添加清理过期缓存的方法
    func cleanExpiredCache() {
        let fileManager = FileManager.default
        guard let files = try? fileManager.contentsOfDirectory(at: cacheDirectory, includingPropertiesForKeys: nil) else {
            return
        }
        
        let now = Date()
        for file in files {
            guard let data = try? Data(contentsOf: file),
                  let cache = try? JSONDecoder().decode(SubtitleCache.self, from: data) else {
                continue
            }
            
            if now.timeIntervalSince(cache.timestamp) > cacheExpiration {
                try? fileManager.removeItem(at: file)
            }
        }
    }
}

class PlayerManager: ObservableObject {
    @Published var player: AVPlayer?
    @Published var currentTime: Double = 0
    @Published var duration: Double = 0
    @Published var isPlaying: Bool = false
    
    private var timeObserver: Any?
    
    func setupPlayer(with url: URL) {
        // 清理旧的播放器
        cleanup()
        
        // 创建新的播放器
        player = AVPlayer(url: url)
        
        // 设置时间观察器
        setupTimeObserver()
        
        // 获取媒体时长
        let asset = AVAsset(url: url)
        duration = CMTimeGetSeconds(asset.duration)
        
        // 添加播放结束通知
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(playerDidFinishPlaying),
            name: .AVPlayerItemDidPlayToEndTime,
            object: player?.currentItem
        )
    }
    
    private func setupTimeObserver() {
        guard let player = player else { return }
        
        let interval = CMTime(seconds: 0.1, preferredTimescale: 1000)
        timeObserver = player.addPeriodicTimeObserver(
            forInterval: interval,
            queue: .main
        ) { [weak self] time in
            self?.currentTime = CMTimeGetSeconds(time)
            self?.isPlaying = player.rate != 0
        }
    }
    
    @objc private func playerDidFinishPlaying() {
        isPlaying = false
        currentTime = 0
        player?.seek(to: .zero)
    }
    
    func cleanup() {
        if let timeObserver = timeObserver, let player = player {
            player.removeTimeObserver(timeObserver)
        }
        timeObserver = nil
        player = nil
        currentTime = 0
        duration = 0
        isPlaying = false
        
        // 移除通知观察者
        NotificationCenter.default.removeObserver(self)
    }
    
    // 播放控制方法
    func play() {
        player?.play()
        isPlaying = true
    }
    
    func pause() {
        player?.pause()
        isPlaying = false
    }
    
    func seek(to time: Double) {
        let targetTime = CMTime(seconds: time, preferredTimescale: 600)
        player?.seek(to: targetTime, toleranceBefore: .zero, toleranceAfter: .zero)
    }
    
    deinit {
        cleanup()
    }
}

// 🔧 界面数据隔离管理器
class InterfaceDataManager: ObservableObject {
    @Published var selectedVideo: URL?
    @Published var segments: [EditableSegment] = []
    @Published var playerManager = PlayerManager()

    let interfaceType: InterfaceType

    init(interfaceType: InterfaceType) {
        self.interfaceType = interfaceType
    }

    // 🔧 深拷贝段落数据，确保完全隔离
    func setSegments(_ newSegments: [EditableSegment]) {
        self.segments = newSegments.map { segment in
            EditableSegment(
                id: UUID(), // 新的ID，确保完全独立
                words: segment.words.map { word in
                    EditableWord(
                        word: word.word,
                        start: word.start,
                        end: word.end,
                        probability: word.probability
                    )
                },
                isCJKLanguage: segment.isCJKLanguage
            )
        }
    }

    // 🔧 设置视频并创建独立的播放器
    func setVideo(_ url: URL) {
        selectedVideo = url
        playerManager.setupPlayer(with: url)
        loadCache()
    }

    // 🔧 加载对应界面的缓存
    func loadCache() {
        guard let mediaURL = selectedVideo else { return }

        if let cachedSegments = SubtitleCacheManager.shared.getCachedSubtitles(
            for: mediaURL,
            interface: interfaceType
        ) {
            setSegments(cachedSegments)
            print("已加载 \(interfaceType.rawValue) 界面缓存")
        }
    }

    // 🔧 保存对应界面的缓存
    func saveCache() {
        guard let mediaURL = selectedVideo, !segments.isEmpty else { return }

        SubtitleCacheManager.shared.cacheSubtitles(
            segments,
            for: mediaURL,
            interface: interfaceType
        )
        print("已保存 \(interfaceType.rawValue) 界面缓存")
    }
}

@available(macOS 15.0, *)
struct ContentView: View {
    @State private var whisperKit: WhisperKit?
    @State private var selectedVideo: URL?
    @State private var recognitionLevel: VNRequestTextRecognitionLevel = .accurate
    @AppStorage("recognitionLanguage") private var recognitionLanguage: String = "auto" // 默认选择英文
    @AppStorage("transcriptionLanguage") private var transcriptionLanguage: String = "en"
    @State private var isTranscribing = false // 控制转录进度指示器的显示
    // @State private var transcriptionText: String = ""
    @State private var searchKeyword = "" // 搜索关键词
    @State private var translationSearchKeyword = "" // 翻译结果搜索关键词
    @State private var detectedFrames: [DetectedFrame] = [] // 存储所有检测到的帧
    @State private var transcriptionResults: [String] = [] // 存储转录结果
    @State private var player: AVPlayer?
    @State private var selectedSegment = 1 // 控制显示的分段
    @AppStorage("selectedModel") private var selectedModel = "small" // 选择模型
    @State private var progress: Double = 0.0 // 进度条的值
    @State private var modelPath: String?
    @State private var srtContent: String = "" // 存储 SRT 格式的内容
    @State private var editableSegments: [EditableSegment] = []// 确保定义
    @State private var selectedSegmentId: UUID?
    @State private var cursorPosition: Int?
    @State private var editHistory = EditHistory()
    @State private var draggedSegmentId: UUID?
    @State private var clipStartTime: String = "00:00:00"
    @State private var clipEndTime: String = "00:00:00"
    @FocusState private var startTimeFocused: Bool
    @FocusState private var endTimeFocused: Bool
    @State private var showTranslation = false
    @State private var configuration: TranslationSession.Configuration?
    @State private var selectedFrames: Set<UUID> = []
    @State private var mediaType: MediaType = .none // 用于区分媒体类型
    @State private var isPlaying: Bool = false
    @State private var currentTime: Double = 0
    @State private var duration: Double = 0
    @State private var timer: Timer?
    @State private var selectedTranslationSegments: Set<UUID> = [] // 存储选中的段落 ID
    @State private var optimizedSegments: [EditableSegment] = [] // AI界面的优化段落
    @State private var extractOptimizedSegments: [EditableSegment] = [] // 提取硬字幕的独立段落
    @State private var cacheUpdateTimer: Timer? // 添加定时器
    @Environment(\.scenePhase) private var scenePhase // 添加场景阶段监测
    @State private var showSettings = false

    // 🔑 转录设置对话框
    @State private var showTranscriptionSettings = false
    @AppStorage("enableVAD") private var enableVAD = true

    // 添加字幕显示模式
    @State private var subtitleMode: SubtitleMode = .none
    @State private var isProcessing: Bool = false //控制视频处理进度指示器的显示
    @State private var subtitleOverlay: NSTextField? // 添加字幕叠加层
    // 在 ContentView 中添加进度状态
    @State private var detectionProgress: Double = 0
    @State private var isDetecting: Bool = false
    @State private var keywordCount: Int = 0 // 关键词数量

    // 添加状态变量
    @State private var isSelectingSubtitleRegion: Bool = false
    @State private var subtitleRegion: CGRect = .zero
    @State private var isExtractingSubtitles: Bool = false
    @State private var subtitleExtractionProgress: Double = 0

    @StateObject private var videoProcessor = VideoProcessor()
    @StateObject private var playerManager = PlayerManager()

    // 🔧 独立的界面数据管理器
    @StateObject private var detectionDataManager = InterfaceDataManager(interfaceType: .detection)
    @StateObject private var aiDataManager = InterfaceDataManager(interfaceType: .aiOptimization)
    @StateObject private var extractionDataManager = InterfaceDataManager(interfaceType: .extraction)

    @State private var lastScrolledIndex: Int = -1  // 添加这一行
    @State private var scrollProxy: ScrollViewProxy? = nil // 用于获取 ScrollView 的代理

    // 在 ContentView 中添加静态实例以便在 AppDelegate 中访问
    static var shared: ContentView?

    // 添加一个方法来切换标签页
    func switchToTab(_ tab: Int) {
        selectedSegment = tab
    }

    // 添加一个对象来处理缓存
    private class CacheManager {
        static let shared = CacheManager()
        
        func saveAllCache() {
            if let contentView = ContentView.shared {
                contentView.saveAllCache()
            }
        }
    }

    init() {
        ContentView.shared = self
        
        // 使用 CacheManager 来处理缓存保存，每5分钟自动缓存
        Timer.scheduledTimer(withTimeInterval: 300, repeats: true) { _ in
            CacheManager.shared.saveAllCache()
        }
    }

    // 添加新的状态变量来控制主界面显示
    @State private var showMainInterface = true

    var body: some View {
        GeometryReader { geometry in
            if showMainInterface {
                // 主界面 - 两个功能框选择
                VStack(spacing: 30) {
                    Text("欢迎使用MocaSubtitle")
                        .font(.largeTitle)
                        .fontWeight(.bold)
                        .padding(.top, 50)
                    
                    HStack(spacing: 40) {
                        // AI断句/翻译优化功能框
                        Button(action: {
                            selectedSegment = 1
                            showMainInterface = false
                        }) {
                            VStack(spacing: 15) {
                                Image(systemName: "wand.and.stars")
                                    .font(.system(size: 60))
                                    .foregroundColor(.blue)
                                
                                Text("AI断句/翻译优化")
                                    .font(.title2)
                                    .fontWeight(.medium)
                                    .multilineTextAlignment(.center)
                            }
                            .frame(width: 200, height: 200)
                            .background(Color.blue.opacity(0.1))
                            .cornerRadius(20)
                            .overlay(
                                RoundedRectangle(cornerRadius: 20)
                                    .stroke(Color.blue.opacity(0.3), lineWidth: 2)
                            )
                        }
                        .buttonStyle(PlainButtonStyle())
                        
                        // 检测视频文本/提取硬字幕功能框
                        Button(action: {
                            selectedSegment = 0
                            showMainInterface = false
                        }) {
                            VStack(spacing: 15) {
                                Image(systemName: "text.viewfinder")
                                    .font(.system(size: 60))
                                    .foregroundColor(.green)
                                
                                Text("检测视频文本/提取硬字幕")
                                    .font(.title2)
                                    .fontWeight(.medium)
                                    .multilineTextAlignment(.center)
                            }
                            .frame(width: 200, height: 200)
                            .background(Color.green.opacity(0.1))
                            .cornerRadius(20)
                            .overlay(
                                RoundedRectangle(cornerRadius: 20)
                                    .stroke(Color.green.opacity(0.3), lineWidth: 2)
                            )
                        }
                        .buttonStyle(PlainButtonStyle())
                    }
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
                .background(Color(NSColor.windowBackgroundColor))
            } else {
                // 原有的功能界面
                if selectedSegment == 0 {
                    // 检测视频文本/提取硬字幕界面 - 重新布局
                    HSplitView {
                        // 左侧 - 视频预览和控制 (60%)
                        VStack(spacing: 0) {
                            // 返回主界面按钮
                            HStack {
                                Button(action: {
                                    showMainInterface = true
                                }) {
                                    Label("返回主界面", systemImage: "chevron.left")
                                }
                                .buttonStyle(BorderedButtonStyle())

                                Spacer()

                                // 右侧设置按钮
                                Button(action: {
                                    showSettings = true
                                }) {
                                    Label("设置", systemImage: "gear")
                                }
                            }
                            .padding(.horizontal, 16)
                            .padding(.vertical, 8)

                            // 视频预览区域
                            ZStack {
                                if let url = detectionDataManager.selectedVideo {
                                    VideoPlayer(
                                        url: url,
                                        player: detectionDataManager.playerManager.player ?? AVPlayer(url: url),
                                        segments: extractOptimizedSegments, // 显示提取的字幕
                                        currentTime: detectionDataManager.playerManager.currentTime,
                                        subtitleMode: $subtitleMode
                                    )
                                    .frame(minHeight: 300)
                                } else {
                                    Rectangle()
                                        .fill(Color.black.opacity(0.1))
                                        .frame(minHeight: 300)
                                        .overlay(
                                            Text("选择视频文件")
                                                .foregroundColor(.secondary)
                                        )
                                }
                            }
                            .frame(minHeight: 300)
                            .overlay(
                                Group {
                                    if isSelectingSubtitleRegion {
                                        SubtitleRegionSelectionOverlay(
                                            isSelecting: $isSelectingSubtitleRegion,
                                            selectedRegion: $subtitleRegion,
                                            imageSize: CGSize(width: geometry.size.width * 0.4, height: geometry.size.height * 0.6)
                                        )
                                    }
                                    TextBoundingBoxesView(
                                        textSegments: videoProcessor.textSegments,
                                        currentTime: playerManager.currentTime
                                    )
                                    .allowsHitTesting(false) // 防止边界框干扰视频播放器的交互
                                }
                                )

                                HStack {
                                    Button(action: {
                                        isSelectingSubtitleRegion = true
                                    }) {
                                        Label("选择字幕区域", systemImage: "rectangle.dashed")
                                    }
                                    .buttonStyle(BorderedButtonStyle())
                                    .disabled(selectedVideo == nil || isExtractingSubtitles)
                                    
                                    Button(action: {
                                        // 确认选择区域
                                        if isSelectingSubtitleRegion {
                                            isSelectingSubtitleRegion = false // 退出选择模式
                                            // 保存选择的区域到videoProcessor中
                                            videoProcessor.subtitleRegion.rect = subtitleRegion
                                            videoProcessor.subtitleRegion.isSelected = true
                                            
                                            // 确保选择框保持可见
                                            // print("已保存字幕区域: \(subtitleRegion)")
                                        }
                                    }) {
                                        Label("确认区域", systemImage: "checkmark")
                                    }
                                    .buttonStyle(BorderedButtonStyle())
                                    .disabled(!isSelectingSubtitleRegion)
                                    
                                    Button(action: {
                                        if LicenseManager.shared.isPro() { // 替换为 isPro
                                            Task {
                                                await extractSubtitles()
                                            }
                                        } else {
                                            ProFeatureHelper.shared.showProFeatureAlert()
                                        }
                                    }) {
                                        Label("提取字幕 🔒", systemImage: "text.bubble")
                                       // Text("🚧 提取字幕")
                                    }
                                    .buttonStyle(BorderedButtonStyle())
                                    // 暂时关闭提取字幕功能
                                    // .disabled(true)
                                    .disabled(selectedVideo == nil || !videoProcessor.subtitleRegion.isSelected || isExtractingSubtitles)
                                    .overlay(
                                        Group {
                                            if isExtractingSubtitles {
                                                VStack {
                                                    ProgressView(value: subtitleExtractionProgress, total: 100)
                                                        .progressViewStyle(.linear)
                                                    Text("\(Int(subtitleExtractionProgress))%")
                                                        .font(.caption)
                                                }
                                                .frame(width: 100)
                                                .padding()
                                            }
                                        }
                                    )
                                    
                                    // 新增：高效硬字幕提取按钮
                                    // Button(action: {
                                    //     Task {
                                    //         await extractHardSubtitlesEfficiently()
                                    //     }
                                    // }) {
                                    //     Text("高效提取硬字幕")
                                    // }
                                    // .buttonStyle(BorderedButtonStyle())
                                    // .disabled(selectedVideo == nil || isExtractingSubtitles)
                                    // .overlay(
                                    //     Group {
                                    //         if isExtractingSubtitles {
                                    //             VStack {
                                    //                 ProgressView(value: subtitleExtractionProgress, total: 100)
                                    //                     .progressViewStyle(.linear)
                                    //                 Text("\(Int(subtitleExtractionProgress))%")
                                    //                     .font(.caption)
                                    //             }
                                    //             .frame(width: 100)
                                    //             .padding()
                                    //         }
                                    //     }
                                    // )
                                }

                                HStack(spacing: 16){
                                    Button("选择视频文件") {
                                        selectMedia()
                                    }

                                    // 识别级别选择
                                    Picker("识别级别", selection: $recognitionLevel) {
                                        Text("速度").tag(VNRequestTextRecognitionLevel.fast)
                                        Text("准确").tag(VNRequestTextRecognitionLevel.accurate)
                                    }
                                    .pickerStyle(SegmentedPickerStyle())
                                    

                                    HStack {
                                        // 别语言选择
                                        Picker("识别语言", selection: $recognitionLanguage) {
                                            Text("英语").tag("en-US")
                                            Text("中文").tag("zh-Hans")
                                            Text("自动").tag("auto")
                                        }
                                        .pickerStyle(MenuPickerStyle())
                                        .disabled(recognitionLevel != .accurate)  // 只有在准确模式下才能选择语言
                                        .opacity(recognitionLevel == .accurate ? 1.0 : 0.5)  // 视觉反馈
                                    }

                                    Button("检测视频文本") {
                                        // 判断是否选择了视频文件
                                        if selectedVideo == nil || mediaType != .video {
                                            let alert = NSAlert()
                                            alert.messageText = "请先选择视频文件"
                                            alert.informativeText = "检测视频文本功能仅支持视频文件。"
                                            alert.addButton(withTitle: "确定")
                                            alert.runModal()
                                            return
                                        }
                                        // 原有检测逻辑保持不变
                                        Image(systemName: "text.book.closed")
                                        Task {
                                            isDetecting = true
                                            detectionProgress = 0
                                            // 强制使用准确模式进行检测
                                            recognitionLevel = .accurate
                                            detectedFrames = try await videoProcessor.processVideo(
                                                url: selectedVideo!,
                                                recognitionLevel: recognitionLevel,
                                                progressHandler: { progress in
                                                    // 在主线程更新进度
                                                    DispatchQueue.main.async {
                                                        detectionProgress = progress
                                                    }
                                                }
                                            )
                                            isDetecting = false
                                        }
                                        selectedSegment = 0 // 设置为检测文本结果
                                    }
                                    .buttonStyle(BorderedButtonStyle())
                                    .disabled(isDetecting)
                                    .overlay(
                                        Group {
                                            if isDetecting {
                                                VStack {
                                                    ProgressView(value: detectionProgress, total: 100)
                                                        .progressViewStyle(.linear)
                                                    Text("\(Int(detectionProgress))%")
                                                        .font(.caption)
                                                }
                                                .frame(width: 100)
                                                .padding()
                                            }
                                        }
                                    )
                                }
                                .padding(.horizontal, 16)
                                .padding(.vertical, 8)

                                if isDetecting {
                                    // 添加一个进度指示器，放在视频播放器下面
                                    VStack(alignment: .leading) {
                                        ProgressView(value: detectionProgress, total: 100)
                                            .progressViewStyle(.linear)
                                            .frame(width: 200)
                                        Text("正在检测视频文本... \(Int(detectionProgress))%")
                                            .font(.caption)
                                            .foregroundColor(.white)
                                    }
                                    .padding()
                                    .cornerRadius(8)
                                }
                            
                            // 检测结果列表
                            ScrollView {
                                LazyVGrid(columns: [
                                    GridItem(.flexible()),
                                    GridItem(.flexible()),
                                    GridItem(.flexible())
                                ], spacing: 10) {
                                    ForEach(videoProcessor.textSegments.indices, id: \.self) { index in
                                        let segment = videoProcessor.textSegments[index]
                                        let highlightedText = highlightText(
                                            segment.text,
                                            keyword: searchKeyword
                                        )
                                        
                                        TextSegmentCard(segment: segment, highlightedText: highlightedText) {
                                            playerManager.seek(to: segment.startTime)
                                        }
                                        .transition(.opacity)
                                    }
                                }
                                .padding()
                            }
                            Spacer() // 添加这个将搜索框推到底部
                    
                            // 搜索框始终显示在底部
                            if selectedSegment == 0 { // 只在视频结果标签页显示搜索框
                                HStack {
                                    Image(systemName: "magnifyingglass")
                                        .foregroundColor(.gray)
                                    TextField("输入关键字搜索视频文本...", text: $searchKeyword)
                                        .textFieldStyle(RoundedBorderTextFieldStyle())
                                    Text("找到 \(keywordCount) 个关键词")
                                        .font(.caption)
                                        .foregroundColor(.secondary)
                                }
                                .padding()
                                .onChange(of: searchKeyword) { newValue in
                                    keywordCount = videoProcessor.countKeywords(keyword: newValue)
                                }
                            }
                        }
                        .frame(width: geometry.size.width * 0.6)
                        
                        // 右侧 - 检测结果 (40%)
                        AITranslationView(
                            originalSegments: editableSegments, // 传递转录结果作为原始段落
                            optimizedSegments: $extractOptimizedSegments,
                            transcriptionLanguage: transcriptionLanguage,
                            player: Binding(
                                get: { playerManager.player },
                                set: { playerManager.player = $0 }
                            ),
                            subtitleMode: $subtitleMode,
                            isTranscribing: $isTranscribing, // 使用 $isTranscribing 传递绑定
                            onReTranscribe: { segment in
                                reTranscribeSegment(segment)
                            },
                            interfaceType: .detection, // 🔧 指定为检测界面
                            instanceId: "detection-interface" // 检测界面实例标识符
                        )
                        .frame(width: geometry.size.width * 0.4)
                    }
                } else if selectedSegment == 1 {
                    // AI断句/翻译优化界面
                    VStack(spacing: 0) {
                        // 返回主界面按钮
                        HStack {
                            Button(action: {
                                showMainInterface = true
                            }) {
                                Label("返回主界面", systemImage: "chevron.left")
                            }
                            .buttonStyle(BorderedButtonStyle())

                            Spacer()

                            // 右侧设置按钮
                            Button(action: {
                                showSettings = true
                            }) {
                                Label("设置", systemImage: "gear")
                            }
                        }
                        .padding(.horizontal, 16)
                        .padding(.vertical, 8)

                        // 左侧内容
                        HSplitView{
                            VStack(spacing: 0) {
                                // 媒体播放器
                                if let url = aiDataManager.selectedVideo {
                                    if mediaType == .video {
                                        VStack {
                                            VideoPlayer(
                                                url: url,
                                                player: aiDataManager.playerManager.player ?? AVPlayer(url: url),
                                                segments: aiDataManager.segments, // 使用独立的段落数据
                                                currentTime: aiDataManager.playerManager.currentTime,
                                                subtitleMode: $subtitleMode
                                            )
                                            
                                            // 添加字幕控制按钮
                                            HStack {
                                                SubtitleControlButton(subtitleMode: $subtitleMode)
                                                    .disabled(!optimizedSegments.contains(where: { segment in
                                                        !segment.text.isEmpty && (segment.translatedText?.isEmpty == false)
                                                    }))

                                                // 播放控制
                                                Button(action: {
                                                    if playerManager.isPlaying {
                                                        playerManager.pause()
                                                    } else {
                                                        playerManager.play()
                                                    }
                                                }) {
                                                    Image(systemName: playerManager.isPlaying ? "pause.fill" : "play.fill")
                                                    Text(playerManager.isPlaying ? "暂停" : "播放")
                                                }
                                                .buttonStyle(BorderedButtonStyle())
                                            }
                                        }
                                        .frame(maxWidth: .infinity, maxHeight: .infinity)
                                    } else if mediaType == .audio {
                                        AudioPlayer(
                                            url: url,
                                            player: playerManager.player ?? AVPlayer(url: url),
                                            segments: optimizedSegments,//转录的
                                            currentTime: playerManager.currentTime // 传递 binding
                                        )
                                        .frame(maxWidth: .infinity, maxHeight: .infinity) // 让容器占满可用空间
                                    }
                                } else {
                                    VStack {
                                        Text("请选择视频或音频文件")
                                            .foregroundColor(.secondary)
                                            // .font(.title2)
                                    }
                                    .frame(maxWidth: .infinity, maxHeight: .infinity)
                                    .background(Color.gray.opacity(0.2))
                                }
                                
                                // 选择媒体按钮
                                HStack {
                                    Button("选择本地媒体") {
                                        selectMedia()
                                    }
   
                                    Button("转录音频") {
                                        showTranscriptionSettings = true
                                    }
                                    .buttonStyle(BorderedButtonStyle())
                                    .sheet(isPresented: $showTranscriptionSettings) {
                                        TranscriptionSettingsView(
                                            transcriptionLanguage: $transcriptionLanguage,
                                            selectedModel: $selectedModel,
                                            enableVAD: $enableVAD,
                                            onConfirm: {
                                                showTranscriptionSettings = false
                                                Task {
                                                    await transcribeAudio()
                                                }
                                                selectedSegment = 1 // 设置为AI翻译优化标签页
                                            },
                                            onCancel: {
                                                showTranscriptionSettings = false
                                            }
                                        )
                                    }
                                    
                                    // 添加导出带字幕视频的按钮
                                    // Button(action: {
                                    //     // exportVideoWithSubtitles()
                                    //     // 这里将来会实现导出带字幕视频的功能
                                    // }) {
                                    //     Label {
                                    //         Text("导出带字幕视频")
                                    //     } icon: {
                                    //         Text("🚧") // 直接使用文本符号
                                    //     }
                                    // }
                                    // .disabled(true) // 这个按钮当前不可用
                                    // .buttonStyle(.bordered)
                                }
                                .padding(.horizontal, 16)
                                .padding(.vertical, 8)
                                
                                // 视频片段选择器
                                HStack(spacing: 16) {                        
                                    HStack(spacing: 8) {
                                        Text("开始")
                                        TextField("00:00:00", text: $clipStartTime)
                                            .textFieldStyle(RoundedBorderTextFieldStyle())
                                            .frame(width: 80)
                                            .onChange(of: clipStartTime) { _ in
                                                updatePlayerTime(from: clipStartTime)
                                            }
                                        
                                        Text("结束")
                                        TextField("00:00:00", text: $clipEndTime)
                                            .textFieldStyle(RoundedBorderTextFieldStyle())
                                            .frame(width: 80)
                                            .onChange(of: clipEndTime) { _ in
                                                updatePlayerTime(from: clipEndTime)
                                            }
                                        
                                        Button(action: {
                                            exportVideoClip()
                                        }) {
                                            Label("导出片段", systemImage: "square.and.arrow.up")
                                                .labelStyle(TitleAndIconLabelStyle())
                                        }
                                        .disabled(selectedVideo == nil)
                                    }
                                }
                                .padding(.horizontal, 16)
                                .padding(.vertical, 8)
                            }
                            .frame(width: geometry.size.width * 0.4)

                            VStack(spacing: 0) {
                                AITranslationView(
                                    originalSegments: editableSegments,
                                    optimizedSegments: $optimizedSegments, // 使用独立的数据
                                    transcriptionLanguage: transcriptionLanguage,
                                    player: Binding(
                                        get: { playerManager.player },
                                        set: { playerManager.player = $0 }
                                    ),
                                    subtitleMode: $subtitleMode,
                                    isTranscribing: $isTranscribing,
                                    onReTranscribe: { segment in
                                        reTranscribeSegment(segment)
                                    },
                                    interfaceType: .aiOptimization, // 🔧 指定为AI优化界面
                                    instanceId: "ai-optimization-interface" // AI优化界面实例标识符
                                )
                            }
                            .frame(width: geometry.size.width * 0.6)
                            .onAppear {
                                // 当AI优化界面出现时，初始化独立的数据
                                if optimizedSegments.isEmpty && !optimizedSegments.isEmpty {
                                    optimizedSegments = optimizedSegments
                                }
                            }
                        }
                    }
                }
            }
        }
        .onChange(of: scenePhase) { newPhase in
            switch newPhase {
            case .background:
                // 应用进入后台时保存所有缓存
                saveAllCache()
            case .inactive:
                // 应用变为非活动状态时保存所有缓存
                saveAllCache()
            default:
                break
            }
        }
        .onAppear {
            setupPlayer()
            // 在应用启动时加载缓存
            if let mediaURL = selectedVideo {
                loadAllCache()
            }
            //自动同步刷新授权
            Task {
                await LicenseManager.shared.updateLicenseStatusAll()
            }
            // 监听 App 失去焦点、即将退出、窗口关闭等事件，自动保存缓存
            NotificationCenter.default.addObserver(
                forName: NSApplication.willResignActiveNotification,
                object: nil,
                queue: .main
            ) { _ in
                saveAllCache()
            }
            NotificationCenter.default.addObserver(
                forName: NSApplication.willTerminateNotification,
                object: nil,
                queue: .main
            ) { _ in
                saveAllCache()
            }
            NotificationCenter.default.addObserver(
                forName: NSWindow.willCloseNotification,
                object: nil,
                queue: .main
            ) { _ in
                saveAllCache()
            }
        }
        .onReceive(NotificationCenter.default.publisher(for: Notification.Name("ForceSaveAllCache"))) { _ in
            saveAllCache()
        }
        .onDisappear {
            // 移除监听
            NotificationCenter.default.removeObserver(self, name: NSApplication.willResignActiveNotification, object: nil)
            NotificationCenter.default.removeObserver(self, name: NSApplication.willTerminateNotification, object: nil)
            NotificationCenter.default.removeObserver(self, name: NSWindow.willCloseNotification, object: nil)
            // 
            timer?.invalidate()
            timer = nil
        }
        .frame(minWidth: 1200, minHeight: 620)
    }

    // 简化数据模型，只保留需要字段
    struct WhisperTranscription: Codable {
        let text: String
        let segments: [Segment]
        let language: String
        
        struct Segment: Codable {
            let start: Double
            let end: Double
            let text: String
            let words: [Word]
            
            // 添加标准初始化方法
            init(start: Double, end: Double, text: String, words: [Word]) {
                self.start = start
                self.end = end
                self.text = text
                self.words = words
            }
            
            // 提供默认空数组，处理缺失words字段的情况
            init(from decoder: Decoder) throws {
                let container = try decoder.container(keyedBy: CodingKeys.self)
                start = try container.decode(Double.self, forKey: .start)
                end = try container.decode(Double.self, forKey: .end)
                text = try container.decode(String.self, forKey: .text)
                
                // 尝试解码words字段，如果不存在则使用空数组
                words = (try? container.decodeIfPresent([Word].self, forKey: .words)) ?? []
            }
            
            enum CodingKeys: String, CodingKey {
                case start, end, text, words
            }
        }
        
        struct Word: Codable {
            let word: String
            let start: Double
            let end: Double
            let probability: Double
        }
    }

    // 翻译结果处理
    private func updateTranslation(response: TranslationSession.Response) {
        guard let index = editableSegments.firstIndex(where: { $0.id.uuidString == response.clientIdentifier }) else {
            return
        }

        // 在保存翻译结果时就处理好格式
        let formattedText = response.targetText
            .components(separatedBy: .whitespaces)
            .filter { !$0.isEmpty }
            .joined(separator: " ")
            .trimmingCharacters(in: .whitespaces)
            editableSegments[index].translatedText = response.targetText // 更新翻译结果
        }

    // 导出字幕
    private func exportSubtitles(type: SubtitleExportType) {
        var srtContent = ""
        for (index, segment) in editableSegments.enumerated() {
            // 处理原始文本，确保单词之间只有一个空格，并去除首尾空格
            let originalText = segment.text
                .components(separatedBy: .whitespaces)
                .filter { !$0.isEmpty }
                .joined(separator: " ") 
                .trimmingCharacters(in: .whitespaces) 
            
            srtContent += "\(index + 1)\n"
            srtContent += "\(formatTimeForSRT(seconds: segment.startTime)) --> \(formatTimeForSRT(seconds: segment.endTime))\n"
            
            switch type {
            case .bilingual:
                // 如果有翻译文本，显示双语
                if let translatedText = segment.translatedText {
                    srtContent += "\(translatedText)\n" // 翻译文本
                    srtContent += "\(originalText)\n" // 原文
                } else {
                    srtContent += "\(originalText)\n" // 只有原文
                }
            case .original:
                // 只导出原文
                srtContent += "\(originalText)\n"
            case .translation:
                // 只导出翻译（如果有）
                if let translatedText = segment.translatedText {
                    srtContent += "\(translatedText)\n"
                } else {
                    srtContent += "\(originalText)\n" // 如果没有翻译，使用原文
                }
            }
            
            srtContent += "\n"
        }
        
        // 保存到文件
        let savePanel = NSSavePanel()
        savePanel.allowedContentTypes = [.text]
        
        // 根据导出类型设置默认文件名
        let filename = switch type {
            case .bilingual: "bilingual_subtitles.srt"
            case .original: "original_subtitles.srt"
            case .translation: "translation_subtitles.srt"
        }
        savePanel.nameFieldStringValue = filename
        
        savePanel.begin { result in
            if result == .OK, let url = savePanel.url {
                do {
                    try srtContent.write(to: url, atomically: true, encoding: .utf8)
                    // print("字幕已保存到: \(url.path)")
                } catch {
                    print("保存字幕文件失败: \(error)")
                }
            }
        }
    }

    // 清理文本
    func cleanText(_ text: String) -> String {
        // 使用正则表达式去除尖括号及其内部的内容
        let cleanedText = text.replacingOccurrences(of: "<[^>]+>", with: "", options: .regularExpression)
        return cleanedText.trimmingCharacters(in: .whitespacesAndNewlines) // 去除首尾空格
    } 
    
    // 选择视频
    func selectVideo() {
        let panel = NSOpenPanel()
        panel.allowedContentTypes = [UTType.movie]
        panel.allowsMultipleSelection = false
        
        if panel.runModal() == .OK {
            guard let selectedVideoURL = panel.url else { return }
            selectedVideo = selectedVideoURL
            player = AVPlayer(url: selectedVideo!)

            // 使用 async/await 处理视频处理
            Task(priority: .userInitiated) { // 设置优先级为 userInitiated
                do {
                    // 处理视频并获取检测到的帧
                    detectedFrames = try await videoProcessor.processVideo(url: selectedVideo!, recognitionLevel: recognitionLevel)
                } catch {
                    print("处理视失败: \(error)")
                }
            }
        }
    }

    // 格式化时间
    func formatTime(seconds: Double) -> String {
        let totalSeconds = Int(seconds)
        let hours = totalSeconds / 3600
        let minutes = (totalSeconds % 3600) / 60
        let secs = totalSeconds % 60
        
        if hours > 0 {
            return String(format: "%02d:%02d:%02d", hours, minutes, secs)
        } else {
            return String(format: "%02d:%02d", minutes, secs)
        }
    }
    
    // 首先定义一个用于写入 JSON 文件的函数
    func writeJSONFile(result: TranscriptionResult) -> Result<URL, Error> {
        do {
            // 使用临时目录
            let tempDirectory = FileManager.default.temporaryDirectory
            let timestamp = ISO8601DateFormatter().string(from: Date())
            let jsonURL = tempDirectory.appendingPathComponent("transcription_\(timestamp).json")
            
            // 创建 JSON 编码器
            let jsonEncoder = JSONEncoder()
            jsonEncoder.outputFormatting = .prettyPrinted
            
            // 编码并写入文件
            let jsonData = try jsonEncoder.encode(result)
            try jsonData.write(to: jsonURL)
            
            return .success(jsonURL)
        } catch {
            return .failure(error)
        }
    }

    // 自动检测本地模型，没有就联网下载
    func ensureWhisperModel(modelName: String = "base") async throws -> WhisperKit {
        // print("开始加载模型...\(modelName)")
        let modelRepo = "argmaxinc/whisperkit-coreml" // 你实际用的repo
        if let savedModelPath = UserDefaults.standard.string(forKey: "WhisperKitModelPath")?.replacingOccurrences(of: "openai_whisper-[^.]+", with: "openai_whisper-\(modelName)", options: .regularExpression),
           FileManager.default.fileExists(atPath: savedModelPath) {
            print("本地模型路径：\(savedModelPath)")
            // 本地模型存在，直接加载
            // 初始化 WhisperKit
            let whisperKit = try await WhisperKit(
                // model: modelName,  
                modelFolder: savedModelPath,//本地模型存储路径（String?），如果为 nil，则使用默认路径或自动下载到默认目录。
                logLevel: .debug, 
                prewarm: true, 
                load: true)
            // print("WhisperKit 离线模型加载完成！")
            return whisperKit
        } else {
            // 本地模型不存在，联网下载
            // print("本地模型不存在，开始联网下载...")
            let whisperKit = try await WhisperKit(
                model: modelName, 
                verbose: true, //是否输出详细日志
                logLevel: .info, //日志级别，常见有 .debug（开发调试）、.info（正式环境）、.warning、.error。
                prewarm: true, //是否预热模型（提前加载到内存，推理更快）。
                load: true) //是否立即加载模型
            guard let downloadedModelPath = whisperKit.modelFolder?.path else {
                throw NSError(domain: "com.example.VideoTextDetector", code: -2, userInfo: [NSLocalizedDescriptionKey: "模型下载后路径获取失败"])
            }
            UserDefaults.standard.set(downloadedModelPath, forKey: "WhisperKitModelPath")
            // print("模型下载完成，已保存路径: \(downloadedModelPath)")
            return whisperKit
        }
    }

    // 修改转录函数
    func transcribeAudio() async {
        // 🔑 最终的专业版权限检查（双重保障）
        if ProFeatureHelper.shared.isProModel(selectedModel) && !LicenseManager.shared.isPro() {
            // 这种情况理论上不应该发生，因为设置对话框已经处理了
            // 但作为最后的安全检查
            DispatchQueue.main.async {
                let alert = NSAlert()
                alert.messageText = "无法使用专业模型"
                alert.informativeText = "检测到您尝试使用专业模型 \"\(self.selectedModel)\"，但当前没有专业版权限。将自动切换到免费模型。"
                alert.addButton(withTitle: "确定")
                alert.runModal()

                self.selectedModel = "small" // 回退到免费模型
            }
            return
        }
        
        // 确保在主线程上设置状态
        await MainActor.run {
            isTranscribing = true // 开始转录时显示进度指示器
        }
        
        var temporaryAudioPath: String? = nil
        
        Task {
            do {
                // 根据媒体类型获取音频路径
                let audioPath: String
                if mediaType == .video {
                    // 如果是视频，需要先提取音频
                    audioPath = try extractAudio(from: selectedVideo!)
                    temporaryAudioPath = audioPath
                    // print("临时音频文件已创建：\(audioPath)")   
                } else if mediaType == .audio {
                    // 如果是音频文件，直接使用其路径
                    audioPath = selectedVideo!.path
                } else {
                    throw NSError(domain: "com.example.VideoTextDetector", 
                                code: -1, 
                                userInfo: [NSLocalizedDescriptionKey: "未选择媒体文件"])
                }

                // defer 块会在函数结束时执行，无论是正常结束还是抛出错误
                defer {
                    // 清理临时音频文件
                    if let tempPath = temporaryAudioPath {
                        do {
                            try FileManager.default.removeItem(atPath: tempPath)
                            // print("已删除临时音频文件：\(tempPath)")
                        } catch {
                            print("删除临时音频文件失败：\(error.localizedDescription)")
                        }
                    }
                    // 在主线程上更新状态
                    Task { @MainActor in
                        isTranscribing = false
                    }
                }

                // 自动检测/下载模型
                do {
                    let whisperKit = try await ensureWhisperModel(modelName: selectedModel)
                    // 模型加载成功弹窗
                    // DispatchQueue.main.async {
                    //     let alert = NSAlert()
                    //     alert.messageText = "模型加载成功"
                    //     alert.informativeText = "WhisperKit模型 \(selectedModel) 已成功加载。"
                    //     alert.addButton(withTitle: "确定")
                    //     alert.runModal()
                    // }

                    // 🔑 根据用户设置配置转录选项
                    let options = DecodingOptions(
                        verbose: true,
                        task: .transcribe,
                        language: transcriptionLanguage,
                        skipSpecialTokens: true,
                        withoutTimestamps: false,
                        wordTimestamps: true,
                        chunkingStrategy: enableVAD ? .vad : nil 
                    )
                    
                    // 转录音频
                    let transcriptionResults = try await whisperKit.transcribe(
                        audioPath: audioPath,
                        decodeOptions: options
                    )

                    let mergedResult = TranscriptionUtilities.mergeTranscriptionResults(transcriptionResults)
                    // print("合并后的结果: \(mergedResult)")
                    
                    // 保存 JSON 文件并处理转录结果
                    // for result in transcriptionResults {
                    //     let saveResult = writeJSONFile(result: result)
                    //     switch saveResult {
                    //     case .success(let jsonURL):
                    //         // print("JSON 文件已保存到临时目录: \(jsonURL.path)")
                    //         // 使用临时文件创建可编辑段落
                    //         createEditableSegmentsFromJSON(jsonURL)
                    //         self.optimizedSegments = self.editableSegments //每次转录音频后，都会用新结果覆盖 optimizedSegments
                            
                    //         // 可选：在处理完后删除临时文件
                    //         try? FileManager.default.removeItem(at: jsonURL)
                            
                    //     case .failure(let error):
                    //          print("保存 JSON 文件失败: \(error.localizedDescription)")
                    //     }
                    // }

                    let saveResult = writeJSONFile(result: mergedResult)
                    switch saveResult {
                    case .success(let jsonURL):
                        // print("JSON 文件已保存到临时目录: \(jsonURL.path)")
                        // 使用临时文件创建可编辑段落
                        createEditableSegmentsFromJSON(jsonURL)
                        // 🔧 为AI优化界面创建独立的数据副本
                        aiDataManager.setSegments(self.editableSegments)
                        self.optimizedSegments = aiDataManager.segments
                        
                        // 可选：在处理完后删除临时文件
                        try? FileManager.default.removeItem(at: jsonURL)
                        
                    case .failure(let error):
                            print("保存 JSON 文件失败: \(error.localizedDescription)")
                    }
                    
                    // 自动跳转到转录结果界面
                    DispatchQueue.main.async {
                        selectedSegment = 1 // 设置为AI翻译优化标签页
                        isTranscribing = false // 转录完成后隐藏进度指示器
                    }
                } catch {
                    // 模型加载失败弹窗
                    DispatchQueue.main.async {
                        let alert = NSAlert()
                        alert.messageText = "模型加载失败"
                        alert.informativeText = "WhisperKit模型加载失败：\(error.localizedDescription)"
                        alert.addButton(withTitle: "确定")
                        alert.runModal()
                    }
                    throw error
                }
            } catch {
                // print("转录错误: \(error.localizedDescription)")
                // 确保在错误发生时也在主线程上更新状态
                await MainActor.run {
                    isTranscribing = false
                }
            }
        }
    }
    
    func formatTimeForSRT(seconds: Double) -> String {
        let hours = Int(seconds) / 3600
        let minutes = (Int(seconds) % 3600) / 60
        let secs = Int(seconds) % 60
        let milliseconds = Int((seconds - Double(Int(seconds))) * 1000)
        
        return String(format: "%02d:%02d:%02d,%03d", hours, minutes, secs, milliseconds)
    }

    func extractAudio(from videoURL: URL) throws -> String {
        // 提取音频的逻辑
        let asset = AVAsset(url: videoURL)
        let outputURL = FileManager.default.temporaryDirectory.appendingPathComponent(UUID().uuidString).appendingPathExtension("m4a")
        
        guard let exportSession = AVAssetExportSession(asset: asset, presetName: AVAssetExportPresetPassthrough) else {
            throw NSError(domain: "com.example.VideoTextDetector", code: -1, userInfo: [NSLocalizedDescriptionKey: "无法创建导出会话"])
        }
        
        exportSession.outputURL = outputURL
        exportSession.outputFileType = .m4a
        
        // 使用 DispatchGroup 等待导出完成
        let group = DispatchGroup()
        group.enter()

        exportSession.exportAsynchronously {
            switch exportSession.status {
            case .completed:
                 print("音频导出成功: \(outputURL.path)")
                // 更新进度
                DispatchQueue.main.async {
                    progress = 100.0 // 假设导出完成时进度为100%
                }
            case .failed:
                 print("音频导出失败: \(exportSession.error?.localizedDescription ?? "未知错误")")
            default:
                 print("音频导出状态: \(exportSession.status)")
            }
            group.leave()
        }
        
        group.wait() // 等待导出完成
        return outputURL.path // 返回出路径
    }
    
    func searchTranscriptionResults(keyword: String) -> [String] {
        guard !keyword.isEmpty else { return transcriptionResults }
        return transcriptionResults.filter { segment in
            segment.lowercased().contains(keyword.lowercased())
        }
    }

    func selectSegment(_ id: UUID) {
        selectedSegmentId = id
        cursorPosition = nil
    }
    
    func mergeSegments() {
        saveState() // 保存当状态
        guard let currentIndex = editableSegments.firstIndex(where: { $0.id == selectedSegmentId }),
              currentIndex < editableSegments.count - 1 else { return }
        
        var mergedSegment = editableSegments[currentIndex]
        let nextSegment = editableSegments[currentIndex + 1]
        
        mergedSegment.words.append(contentsOf: nextSegment.words)
        
        editableSegments.remove(at: currentIndex + 1)
        editableSegments[currentIndex] = mergedSegment
        
        // 添加缓存更新
        scheduleCacheUpdate()
    }
    
    func splitSegmentAtCursor() {
        saveState() // 存当前状态
        guard let currentIndex = editableSegments.firstIndex(where: { $0.id == selectedSegmentId }),
              let cursor = cursorPosition else { return }
        
        let segment = editableSegments[currentIndex]
        let text = segment.text
        
        // 在光标位分割文本
        let index = text.index(text.startIndex, offsetBy: cursor)
        let firstPart = String(text[..<index])
        let secondPart = String(text[index...])
        
        // 创建新的段落
        let firstSegment = EditableSegment(words: [
            EditableWord(
                word: firstPart.trimmingCharacters(in: .whitespaces),
                start: segment.startTime,
                end: segment.startTime + (segment.endTime - segment.startTime) / 2,
                probability: 1.0
            )
        ])
        
        let secondSegment = EditableSegment(words: [
            EditableWord(
                word: secondPart.trimmingCharacters(in: .whitespaces),
                start: segment.startTime + (segment.endTime - segment.startTime) / 2,
                end: segment.endTime,
                probability: 1.0
            )
        ])
        
        // 更新段落列表
        editableSegments.remove(at: currentIndex)
        editableSegments.insert(contentsOf: [firstSegment, secondSegment], at: currentIndex)
        
        // 更新选中状态
        selectedSegmentId = firstSegment.id
        cursorPosition = nil
        
        // 添加缓存更新
        scheduleCacheUpdate()
    }

    // 从 JSON 创建可编辑段落，解析json
    func createEditableSegmentsFromJSON(_ jsonURL: URL) {
        do {
            let jsonData = try Data(contentsOf: jsonURL)
            let transcription = try JSONDecoder().decode(WhisperTranscription.self, from: jsonData)
            
            // 在主线程更新 UI
            DispatchQueue.main.async {
                let isCJKLanguage = ["ja", "zh", "zh-Hans", "zh-Hant", "ko","th"].contains(transcriptionLanguage)// 使用转录结果中的语言
                
                // 🔑 简单判断：有词级时间戳就用词级，没有就用段落级
                self.editableSegments = transcription.segments.compactMap { segment in
                    if !segment.words.isEmpty {
                        // 有词级时间戳：使用词级处理
                        return EditableSegment(
                            words: segment.words.map { word in
                                EditableWord(
                                    word: cleanText(word.word),
                                    start: word.start,
                                    end: word.end,
                                    probability: word.probability
                                )
                            },
                            isCJKLanguage: isCJKLanguage
                        )
                    } else {
                        // 没有词级时间戳：使用段落级处理
                        return EditableSegment(
                            words: [EditableWord(
                                word: cleanText(segment.text),
                                start: segment.start,
                                end: segment.end,
                                probability: 1.0
                            )],
                            isCJKLanguage: isCJKLanguage
                        )
                    }
                }

                // if let mediaURL = self.selectedVideo {
                //     SubtitleCacheManager.shared.cacheSubtitles(editableSegments, for: mediaURL)
                // }//这里要添加标签页1缓存
                
                // print("已加载 \(self.editableSegments.count) 个字幕段落")
            }
        } catch {
            // print("解析 JSON 文件失败: \(error)")
            // print("详细错误信息: \(error.localizedDescription)")
            
            // 尝试使用更宽松的解析方式
            tryAlternativeJSONParsing(jsonURL)
        }
    }
    
    // 添加一个备用的JSON解析方法，用于处理缺少words字段的情况
    func tryAlternativeJSONParsing(_ jsonURL: URL) {
        do {
            // 定义一个简化的结构，不要求words字段
            struct SimpleWhisperTranscription: Codable {
                let text: String
                let segments: [SimpleSegment]
                let language: String
                
                struct SimpleSegment: Codable {
                    let start: Double
                    let end: Double
                    let text: String
                    // 不包含words字段
                }
            }
            
            let jsonData = try Data(contentsOf: jsonURL)
            let transcription = try JSONDecoder().decode(SimpleWhisperTranscription.self, from: jsonData)
            
            // 在主线程更新UI
            DispatchQueue.main.async {
                let isCJKLanguage = ["ja", "zh", "zh-Hans", "zh-Hant", "ko","th"].contains(transcriptionLanguage)
                
                self.editableSegments = transcription.segments.map { segment in
                    // 所有段落都作为单个单词处理
                    return EditableSegment(
                        words: [EditableWord(
                            word: cleanText(segment.text),
                            start: segment.start,
                            end: segment.end,
                            probability: 1.0
                        )],
                        isCJKLanguage: isCJKLanguage
                    )
                }
                
                if let mediaURL = self.selectedVideo {
                    SubtitleCacheManager.shared.cacheSubtitles(editableSegments, for: mediaURL)
                }
                
                // print("已使用备用方法加载 \(self.editableSegments.count) 个字幕段落")
            }
        } catch {
            print("备用JSON解析也失败: \(error)")
            print("详细错误信息: \(error.localizedDescription)")
        }
    }

    func saveState() {
        let currentState = EditHistory.State(
            segments: editableSegments,
            selectedId: selectedSegmentId
        )
        editHistory.push(currentState)
    }
    
    // func undo() {
    //     guard let previousState = editHistory.undo() else { return }
    //     editableSegments = previousState.segments
    //     selectedSegmentId = previousState.selectedId
    // }
    
    // func redo() {
    //     guard let nextState = editHistory.redo() else { return }
    //     editableSegments = nextState.segments
    //     selectedSegmentId = nextState.selectedId
    // }

    func exportToSRT() {
        let savePanel = NSSavePanel()
        savePanel.allowedContentTypes = [UTType(filenameExtension: "srt")!]
        savePanel.nameFieldStringValue = "subtitle.srt"
        
        savePanel.begin { result in
            if result == .OK, let url = savePanel.url {
                let srtContent = generateSRTContent()
                do {
                    try srtContent.write(to: url, atomically: true, encoding: .utf8)
                    // print("保存 SRT 文件失败: \(error)")
                } catch {
                    print("保存 SRT 文件失败: \(error)")
                }
            }
        }
    }
    
    func generateSRTContent() -> String {
        var srtContent = ""
        for (index, segment) in editableSegments.enumerated() {
            srtContent += "\(index + 1)\n"
            srtContent += "\(formatTimeForSRT(seconds: segment.startTime)) --> \(formatTimeForSRT(seconds: segment.endTime))\n"
            
            // 处理原始文本，确保单词之间只有一个空格，并去除首尾空格
            let originalText = segment.text
                .components(separatedBy: .whitespaces)
                .filter { !$0.isEmpty }
                .joined(separator: " ")
                .trimmingCharacters(in: .whitespaces) // 去除首尾空格
                srtContent += "\(originalText)\n\n"
            }
        return srtContent
    }
    
    func exportToJSON() {
        let savePanel = NSSavePanel()
        savePanel.allowedContentTypes = [UTType(filenameExtension: "json")!]
        savePanel.nameFieldStringValue = "subtitle.json"
        
        savePanel.begin { result in
            if result == .OK, let url = savePanel.url {
                let jsonContent = generateJSONContent()
                do {
                    let jsonData = try JSONEncoder().encode(jsonContent)
                    try jsonData.write(to: url)
                } catch {
                    print("保存 JSON 文件失败: \(error)")
                }
            }
        }
    }
    
    func generateJSONContent() -> WhisperTranscription {
        // 将编辑后的段落转换回 WhisperTranscription 格式
        let segments = editableSegments.map { segment in
            WhisperTranscription.Segment(
                start: segment.startTime,
                end: segment.endTime,
                text: segment.text,
                words: segment.words.map { word in
                    WhisperTranscription.Word(
                        word: word.word,
                        start: word.start,
                        end: word.end,
                        probability: word.probability
                    )
                }
            )
        }
        
        return WhisperTranscription(
            text: editableSegments.map { $0.text }.joined(separator: " "),
            segments: segments,
            language: transcriptionLanguage // 使用用户选择的语言
        )
    }

    // 添加辅助函数
    func timeStringToSeconds(_ timeString: String) -> Double? {
        let components = timeString.split(separator: ":").map { String($0) }
        guard components.count == 3,
              let hours = Double(components[0]),
              let minutes = Double(components[1]),
              let seconds = Double(components[2]) else {
            return nil
        }
        return hours * 3600 + minutes * 60 + seconds
    }

    func secondsToTimeString(_ seconds: Double) -> String {
        let hours = Int(seconds) / 3600
        let minutes = (Int(seconds) % 3600) / 60
        let secs = Int(seconds) % 60
        return String(format: "%02d:%02d:%02d", hours, minutes, secs)
    }

    func exportVideoClip() {
        guard let url = selectedVideo else { return }
        
        let startTime = timeStringToSeconds(clipStartTime) ?? 0
        let endTime = timeStringToSeconds(clipEndTime) ?? 0
        
        let asset = AVAsset(url: url)
        
        // 根据媒体类型选择不同的预设
        let preset = mediaType == .video ? AVAssetExportPresetHighestQuality : AVAssetExportPresetAppleM4A
        guard let exportSession = AVAssetExportSession(asset: asset, presetName: preset) else { return }
        
        // 创建保存面板
        let savePanel = NSSavePanel()
        savePanel.allowedContentTypes = [UTType.mpeg4Movie, UTType.audio] // 添加音频类型
        
        // 设置默认文件名（带时间戳）
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyyMMdd_HHmmss"
        let timestamp = dateFormatter.string(from: Date())
        
        // 设置默认文件名
        if mediaType == .video {
            savePanel.nameFieldStringValue = "clip_\(timestamp).mp4" 
        } else if mediaType == .audio {
            savePanel.nameFieldStringValue = "clip_\(timestamp).m4a" // 音频就是导出mp3
        }
        
        // 显示保存面板
        // print("显示保存面板")
        savePanel.begin { result in
            if result == .OK, let outputURL = savePanel.url {
                // 配置导出会话
                exportSession.outputURL = outputURL
                if mediaType == .video {
                    exportSession.outputFileType = .mp4 
                } else if mediaType == .audio {
                    exportSession.outputFileType = .m4a // 音频就是导出mp3
                }
                exportSession.timeRange = CMTimeRange(
                    start: CMTime(seconds: startTime, preferredTimescale: 600),
                    duration: CMTime(seconds: endTime - startTime, preferredTimescale: 600)
                )
                
                // 开始导出
                exportSession.exportAsynchronously {
                    DispatchQueue.main.async {
                        switch exportSession.status {
                        case .completed:
                             print("音视频片段已成功导出到: \(outputURL.path)")
                        case .failed:
                            if let error = exportSession.error {
                                 print("音视频导出失败: \(error.localizedDescription)")
                            }
                        case .cancelled:
                             print("音视频导出被取消")
                        default:
                            break
                        }
                    }
                }
            }
        }
    }

    // 更新播放器时间
    func updatePlayerTime(from timeString: String) {
        if let time = timeStringToSeconds(timeString) {
            playerManager.seek(to: time) // 使用 PlayerManager 的 seek 方法
        }
    }

    // 分割段落逻辑
    func splitSegmentAtIndex(_ wordIndex: Int, in segmentIndex: Int) {
        guard segmentIndex >= 0 && segmentIndex < editableSegments.count else { return }
        
        let segment = editableSegments[segmentIndex]
        let words = segment.words
        
        // 确保索引在有效范围内
        guard wordIndex > 0 && wordIndex < words.count else { return } // 确保 wordIndex 大于 0
        
        // 找到光标位置对应的单词索引
        let splitWords = Array(words[0..<wordIndex])  // 包含当前单词前的所有单词
        let remainingWords = Array(words[wordIndex...])  // 包含当前单词及其后面的所有单词
        
        // 创建新的段落
        let newSegment = EditableSegment(words: remainingWords)
        
        // 更新段落列表
        editableSegments[segmentIndex] = EditableSegment(words: splitWords)  // 更新当前段落
        editableSegments.insert(newSegment, at: segmentIndex + 1)  // 插入新段落
        
        // 更新选中状态
        selectedSegmentId = newSegment.id  // 选中新的段落
    }

    // 合并段落的逻辑
    func mergeSegments(at index: Int) {
        guard index >= 0 && index < editableSegments.count - 1 else { return }
        
        let currentSegment = editableSegments[index]
        let nextSegment = editableSegments[index + 1]
        
        // 合并两个段落的单词
        let mergedWords = currentSegment.words + nextSegment.words
        let mergedSegment = EditableSegment(words: mergedWords)
        
        // 更新段落列表
        editableSegments[index] = mergedSegment
        editableSegments.remove(at: index + 1)  // 移除下一个段落
        
        // 更新选中状态
        selectedSegmentId = mergedSegment.id  // 选中合并后的段落
    }

    // 添加高亮文本的辅助函数
    func highlightText(_ text: String, keyword: String) -> AttributedString {
        guard !keyword.isEmpty else {
            return AttributedString(text)
        }
        
        var attributedString = AttributedString(text)
        
        do {
            // 创建正则表达式
            let regex = try NSRegularExpression(
                pattern: NSRegularExpression.escapedPattern(for: keyword),
                options: .caseInsensitive
            )
            
            // 获取所有匹配
            let matches = regex.matches(
                in: text,
                range: NSRange(location: 0, length: text.count)
            )
            
            // 处理每个匹配
            for match in matches {
                if let range = Range(match.range, in: text) {
                    // 直接在原始的 AttributedString 上设置属性
                    let lowerBound = text.distance(from: text.startIndex, to: range.lowerBound)
                    let upperBound = text.distance(from: text.startIndex, to: range.upperBound)
                    let attributedRange = attributedString.range(of: text[range])
                    
                    if let attributedRange = attributedRange {
                        attributedString[attributedRange].backgroundColor = .yellow
                    }
                }
            }
        } catch {
            print("正则表达式错误: \(error)")
        }
        
        return attributedString
    }

    // 添加过滤逻辑
    var filteredSegments: [TextSegment] {
        if searchKeyword.isEmpty {
            return videoProcessor.textSegments
        }
        return videoProcessor.textSegments.filter { segment in
            segment.text.localizedCaseInsensitiveContains(searchKeyword)
        }
    }

    func exportLongImage() {
        let selectedSegments = videoProcessor.textSegments.filter { selectedFrames.contains($0.id) }
        guard !selectedSegments.isEmpty else { return }
        
        // 获取第一个段落的完整帧
        guard let firstSegment = selectedSegments.first,
              let firstFrame = firstSegment.frames.first else { return }
        
        let width = firstFrame.image.size.width
        let firstFrameHeight = firstFrame.image.size.height
        
        // 计算总高度：第一帧高度 + 后续帧文本区域高度（无额外padding）
        var totalHeight = firstFrameHeight
        for segment in selectedSegments.dropFirst() {
            if let frame = segment.frames.first {
                totalHeight += frame.textBounds.height
            }
        }
        
        // 创建长条形图片
        let finalImage = NSImage(size: NSSize(width: width, height: totalHeight))
        finalImage.lockFocus()
        
        // 从顶部开始绘制第一帧（完整显示）
        firstFrame.image.draw(in: NSRect(
            x: 0,
            y: totalHeight - firstFrameHeight,
            width: width,
            height: firstFrameHeight
        ))
        
        // 当前绘制位置
        var currentY = totalHeight - firstFrameHeight
        
        // 绘制后续帧（只显示文本区域，无缝拼接）
        for segment in selectedSegments.dropFirst() {
            guard let frame = segment.frames.first else { continue }
            
            let textRect = frame.textBounds
            let textAreaHeight = textRect.height
            
            // 计算源图片中文本区域的位置（无padding）
            let sourceRect = NSRect(
                x: 0,
                y: frame.image.size.height - textRect.maxY,
                width: width,
                height: textAreaHeight
            )
            
            // 无缝拼接到上一帧
            currentY -= textAreaHeight
            frame.image.draw(
                in: NSRect(
                    x: 0,
                    y: currentY,
                    width: width,
                    height: textAreaHeight
                ),
                from: sourceRect,
                operation: .sourceOver,
                fraction: 1.0
            )
        }
        
        finalImage.unlockFocus()
        
        // 保存长图
        let savePanel = NSSavePanel()
        savePanel.allowedContentTypes = [UTType.png]
        savePanel.nameFieldStringValue = "exported_image.png"
        
        if savePanel.runModal() == .OK {
            if let url = savePanel.url,
               let imageData = finalImage.tiffRepresentation,
               let bitmapImage = NSBitmapImageRep(data: imageData),
               let pngData = bitmapImage.representation(using: .png, properties: [:]) {
                do {
                    try pngData.write(to: url)
                    // print("长图导出到: \(url.path)")
                    
                    // 导成功后，在主线程清除选中状态
                    DispatchQueue.main.async {
                        selectedFrames.removeAll() // 清除所有选中的卡片
                    }
                } catch {
                    print("导出长图失败: \(error.localizedDescription)")
                }
            }
        } else {
            // 用户取消导出时也清除选中状态
            DispatchQueue.main.async {
                selectedFrames.removeAll()
            }
        }
    }

    // 添加新的方法
    enum MediaType {
        case none
        case video
        case audio
    }

    // 🔧 改进的媒体选择，支持界面隔离
    func selectMedia() {
        let panel = NSOpenPanel()
        panel.allowedContentTypes = [UTType.movie, UTType.audio]
        panel.allowsMultipleSelection = false

        if panel.runModal() == .OK {
            guard let selectedURL = panel.url else { return }
            selectedVideo = selectedURL

            // 🔧 为每个界面设置独立的视频和数据
            detectionDataManager.setVideo(selectedURL)
            aiDataManager.setVideo(selectedURL)
            extractionDataManager.setVideo(selectedURL)

            // 加载缓存前先清空现有段落（重新转录新视频）
            editableSegments = []
            optimizedSegments = []
            extractOptimizedSegments = []

            // 尝试加载所有缓存
            loadAllCache()

            // 判断媒体类型
            let typeIdentifier = try? selectedURL.resourceValues(forKeys: [.typeIdentifierKey]).typeIdentifier
            if let identifier = typeIdentifier {
                if UTType(identifier)?.conforms(to: .audio) ?? false {
                    mediaType = .audio
                } else {
                    mediaType = .video
                }
            }

            setupPlayer()
        }
    }

    func setupPlayer() {
        guard let url = selectedVideo else { 
            print("错误：未选择视频文件")
            return 
        }
        playerManager.setupPlayer(with: url)
    }

    

    func getCurrentSegment() -> EditableSegment? {
        return editableSegments.first { segment in
            currentTime >= segment.startTime && currentTime <= segment.endTime
        }
    }

    // 添加新的辅助函数
    private func toggleSelection(_ id: UUID) {
        if selectedTranslationSegments.contains(id) {
            selectedTranslationSegments.remove(id)
        } else {
            selectedTranslationSegments.insert(id)
        }
    }

    private func deleteSelectedTranslations() {
        // 保存当前状态用于撤销
        saveState()
        
        // 删除选中的段落
        editableSegments.removeAll { segment in
            selectedTranslationSegments.contains(segment.id)
        }
        
        // 清空选择
        selectedTranslationSegments.removeAll()
    }

    // 添加过滤翻译结果的计算属性
    private var filteredTranslationSegments: [EditableSegment] {
        if translationSearchKeyword.isEmpty {
            return editableSegments
        }
        return editableSegments.filter { segment in
            segment.text.localizedCaseInsensitiveContains(translationSearchKeyword) ||
            (segment.translatedText?.localizedCaseInsensitiveContains(translationSearchKeyword) ?? false)
        }
    }

    // 🔧 改进的缓存加载，支持界面隔离
    func loadAllCache() {
        guard let mediaURL = selectedVideo else { return }

        // 加载原始字幕缓存（AI优化界面）
        if let cachedSegments = SubtitleCacheManager.shared.getCachedSubtitles(
            for: mediaURL,
            interface: .aiOptimization
        ) {
            editableSegments = cachedSegments
            aiDataManager.setSegments(cachedSegments)
            // print("已加载 AI 优化界面缓存")
        }

        // 加载检测界面缓存
        if let cachedDetectionSegments = SubtitleCacheManager.shared.getCachedSubtitles(
            for: mediaURL,
            interface: .detection
        ) {
            detectionDataManager.setSegments(cachedDetectionSegments)
            // print("已加载检测界面缓存")
        }

        // 加载提取界面缓存
        if let cachedExtractionSegments = SubtitleCacheManager.shared.getCachedSubtitles(
            for: mediaURL,
            interface: .extraction
        ) {
            extractionDataManager.setSegments(cachedExtractionSegments)
            extractOptimizedSegments = cachedExtractionSegments
            // print("已加载提取界面缓存")
        }
    }

    // 在 ContentView 中添加更新缓存的方法
    private func updateCache() {
        guard let mediaURL = selectedVideo else { return }
        SubtitleCacheManager.shared.cacheSubtitles(editableSegments, for: mediaURL)
    }

    // 添加定时器调度方法
    private func scheduleCacheUpdate() {
        // 取消之前的定时器
        cacheUpdateTimer?.invalidate()
        
        // 设置新的定时器，延迟60秒更新缓存
        cacheUpdateTimer = Timer.scheduledTimer(withTimeInterval: 60.0, repeats: false) { _ in
            CacheManager.shared.saveAllCache()
        }
    }

    // 🔧 改进的缓存保存，支持界面隔离
    func saveAllCache() {
        // print("开始保存所有缓存数据")
        guard let mediaURL = selectedVideo else { return }

        // 保存AI优化界面字幕
        if !editableSegments.isEmpty {
            SubtitleCacheManager.shared.cacheSubtitles(
                editableSegments,
                for: mediaURL,
                interface: .aiOptimization
            )
            // print("已保存 AI 优化界面缓存")
        }

        // 保存检测界面字幕
        detectionDataManager.saveCache()

        // 保存提取界面字幕
        extractionDataManager.saveCache()

        // 保存各个数据管理器的缓存
        aiDataManager.saveCache()
    }

    // 添加导出带字幕视频的函数
    private func exportVideoWithSubtitles() {
        guard let videoURL = selectedVideo, let player = playerManager.player else { return }
        
        // 创建保存面板
        let savePanel = NSSavePanel()
        savePanel.allowedContentTypes = [UTType.mpeg4Movie]
        savePanel.nameFieldStringValue = "video_with_subtitles.mp4"
        
        if savePanel.runModal() == .OK {
            guard let outputURL = savePanel.url else { return }
            
            // 显示进度指示器
            isProcessing = true
            
            // 将导出过程移至后台线程
            DispatchQueue.global(qos: .userInitiated).async {
                do {
                    // 准备源资源
                    let asset = AVAsset(url: videoURL)
                    
                    // 创建导出会话
                    let composition = AVMutableComposition()
                    
                    // 添加视频轨道
                    guard let compositionVideoTrack = composition.addMutableTrack(
                        withMediaType: .video, 
                        preferredTrackID: kCMPersistentTrackID_Invalid
                    ) else {
                        throw NSError(domain: "ExportError", code: -1, userInfo: [NSLocalizedDescriptionKey: "无法创建视频轨道"])
                    }
                    
                    // 添加音频轨道
                    let compositionAudioTrack = composition.addMutableTrack(
                        withMediaType: .audio, 
                        preferredTrackID: kCMPersistentTrackID_Invalid
                    )
                    
                    // 获取源视频轨道
                    guard let videoTrack = asset.tracks(withMediaType: .video).first else {
                        throw NSError(domain: "ExportError", code: -1, userInfo: [NSLocalizedDescriptionKey: "无法获取源视频轨道"])
                    }
                    
                    // 获取源音频轨道（如果有）
                    let audioTracks = asset.tracks(withMediaType: .audio)
                    
                    // 计算视频时长
                    let timeRange = CMTimeRange(start: .zero, duration: asset.duration)
                    
                    // 复制视频轨道
                    try compositionVideoTrack.insertTimeRange(timeRange, of: videoTrack, at: .zero)
                    
                    // 复制音频轨道（如果有）
                    if let audioTrack = audioTracks.first, let compositionAudioTrack = compositionAudioTrack {
                        try compositionAudioTrack.insertTimeRange(timeRange, of: audioTrack, at: .zero)
                    }
                    
                    // 准备字幕合成层
                    let videoSize = videoTrack.naturalSize
                    let layerInstruction = AVMutableVideoCompositionLayerInstruction(assetTrack: compositionVideoTrack)
                    
                    // 确保视频方向正确
                    var transform = videoTrack.preferredTransform
                    layerInstruction.setTransform(transform, at: .zero)
                    
                    // 设置合成指令
                    let instruction = AVMutableVideoCompositionInstruction()
                    instruction.timeRange = timeRange
                    instruction.layerInstructions = [layerInstruction]
                    
                    // 创建视频合成
                    let videoComposition = AVMutableVideoComposition()
                    videoComposition.renderSize = videoSize
                    videoComposition.frameDuration = CMTime(value: 1, timescale: 30) // 30 FPS
                    videoComposition.instructions = [instruction]
                    
                    // 使用Core Animation添加字幕
                    let parentLayer = CALayer()
                    parentLayer.frame = CGRect(x: 0, y: 0, width: videoSize.width, height: videoSize.height)
                    
                    // 源视频层
                    let videoLayer = CALayer()
                    videoLayer.frame = CGRect(x: 0, y: 0, width: videoSize.width, height: videoSize.height)
                    parentLayer.addSublayer(videoLayer)
                    
                    // 字幕层
                    let subtitleLayer = CATextLayer()
                    subtitleLayer.frame = CGRect(
                        x: 0,
                        y: videoSize.height * 0.85, // 位于底部15%位置
                        width: videoSize.width,
                        height: videoSize.height * 0.15
                    )
                    subtitleLayer.alignmentMode = .center
                    subtitleLayer.fontSize = videoSize.width / 30 // 根据视频宽度调整字体大小
                    subtitleLayer.isWrapped = true
                    
                    // 添加字幕背景层以增强可读性
                    let subtitleBackgroundLayer = CALayer()
                    subtitleBackgroundLayer.frame = subtitleLayer.frame
                    subtitleBackgroundLayer.backgroundColor = CGColor(gray: 0, alpha: 0.5)
                    
                    parentLayer.addSublayer(subtitleBackgroundLayer)
                    parentLayer.addSublayer(subtitleLayer)
                    
                    // 设置动画工具
                    videoComposition.animationTool = AVVideoCompositionCoreAnimationTool(
                        postProcessingAsVideoLayer: videoLayer,
                        in: parentLayer
                    )
                    
                    // 创建导出会话
                    let exportSession = AVAssetExportSession(
                        asset: composition,
                        presetName: AVAssetExportPresetHighestQuality
                    )
                    
                    exportSession?.outputURL = outputURL
                    exportSession?.outputFileType = .mp4
                    exportSession?.videoComposition = videoComposition
                    exportSession?.shouldOptimizeForNetworkUse = true
                    
                    // 创建显示字幕的时间表
                    var subtitleDisplayTimes: [(String, CMTime, CMTime)] = [] // (文本, 开始时间, 结束时间)
                    
                    // 根据当前字幕模式选择要添加的字幕
                    for segment in editableSegments {
                        let startTime = CMTime(seconds: segment.startTime, preferredTimescale: 600)
                        let endTime = CMTime(seconds: segment.endTime, preferredTimescale: 600)
                        
                        var subtitleText = ""
                        switch subtitleMode {
                        case .bilingual:
                            if let translatedText = segment.translatedText {
                                subtitleText = "\(translatedText)\n\(segment.text)"
                            } else {
                                subtitleText = segment.text
                            }
                        case .original:
                            subtitleText = segment.text
                        case .translation:
                            if let translatedText = segment.translatedText {
                                subtitleText = translatedText
                            }
                        case .none:
                            subtitleText = ""
                        }
                        
                        subtitleDisplayTimes.append((subtitleText, startTime, endTime))
                    }
                    
                    // 开始导出过程
                    // 首先创建一个空的字幕层
                    subtitleLayer.string = ""
                    
                    // 使用定时器在适当的时间更新字幕内容
                    let completionHandler = exportSession?.exportAsynchronously {
                        DispatchQueue.main.async {
                            isProcessing = false
                            
                            if let error = exportSession?.error {
                                print("导出视频失败: \(error.localizedDescription)")
                            } else {
                                print("带字幕的视频已导出至: \(outputURL.path)")
                            }
                        }
                    }
                    
                    // 循环更新进度和字幕
                    var didComplete = false
                    while !didComplete {
                        // 更新UI进度 - 使用async/await而不是直接调用
                        let progress = exportSession?.progress ?? 0
                        Task { @MainActor in
                            self.progress = Double(progress)
                        }
                        
                        // 计算当前时间
                        let currentExportTime = CMTimeMultiplyByFloat64(
                            asset.duration,
                            multiplier: Float64(progress)
                        )
                        
                        // 更新字幕
                        for (text, startTime, endTime) in subtitleDisplayTimes {
                            if CMTimeCompare(currentExportTime, startTime) >= 0 && 
                               CMTimeCompare(currentExportTime, endTime) <= 0 {
                                // 确保在主线程上更新UI并使用CATransaction防止布局循环
                                DispatchQueue.main.async {
                                    CATransaction.begin()
                                    CATransaction.setDisableActions(true) // 禁用隐式动画
                                    subtitleLayer.string = text
                                    subtitleLayer.foregroundColor = CGColor(red: 1, green: 1, blue: 1, alpha: 1)
                                    CATransaction.commit()
                                }
                                break
                            } else {
                                DispatchQueue.main.async {
                                    CATransaction.begin()
                                    CATransaction.setDisableActions(true)
                                    subtitleLayer.string = ""
                                    CATransaction.commit()
                                }
                            }
                        }
                        
                        // 检查是否已完成
                        didComplete = exportSession?.status != .exporting
                        
                        // 短暂休眠以节省CPU
                        if !didComplete {
                            Thread.sleep(forTimeInterval: 0.1)
                        }
                    }
                } catch {
                    DispatchQueue.main.async {
                        isProcessing = false
                        progress = 0.0
                        // print("导出视频时出错: \(error.localizedDescription)")
                    }
                }
            }
        }
    }

    func createOrUpdateSubtitleOverlay(text: String) {
        // 使用主线程执行UI更新
        DispatchQueue.main.async { [self] in
            // 检查 player 是否有效
            guard let player = self.player else { return }
            
            // 如果字幕叠加层不存在，创建一个
            if self.subtitleOverlay == nil {
                let textField = NSTextField()
                textField.isEditable = false
                textField.isBordered = false
                textField.backgroundColor = NSColor.clear
                textField.textColor = NSColor.white
                textField.font = NSFont.systemFont(ofSize: 20, weight: .medium)
                textField.alignment = .center
                textField.maximumNumberOfLines = 0
                textField.lineBreakMode = .byWordWrapping
                
                // 添加阴影效果使字幕更清晰
                textField.shadow = NSShadow()
                textField.shadow?.shadowColor = NSColor.black
                textField.shadow?.shadowBlurRadius = 2
                textField.shadow?.shadowOffset = NSSize(width: 1, height: 1)
                
                // 确保在主线程上添加子视图
                if Thread.isMainThread {
                    // 获取 VideoPlayer 的视图
                    if let videoPlayerView = NSApp.windows.first?.contentView?.subviews.first(where: { $0 is AVPlayerView }) {
                        videoPlayerView.addSubview(textField)
                        self.subtitleOverlay = textField
                    }
                }
            }
            
            // 更新字幕文本和位置
            if let textField = self.subtitleOverlay,
               let videoPlayerView = textField.superview {  // 确保文本字段仍然附加到视图层次结构中
                CATransaction.begin()
                CATransaction.setDisableActions(true)
                textField.stringValue = text
                textField.sizeToFit()
                
                let x = (videoPlayerView.bounds.width - textField.bounds.width) / 2
                let y = videoPlayerView.bounds.height * 0.15
                textField.frame.origin = NSPoint(x: x, y: y)
                CATransaction.commit()
                
                // 确保字幕视图始终在最前面
                if Thread.isMainThread {
                    videoPlayerView.addSubview(textField)
                }
            }
        }
    }

    // 🔑 优化版本的字幕提取方法
    func extractSubtitles() async {
        guard let url = selectedVideo, videoProcessor.subtitleRegion.isSelected else { return }

        // 文件大小限制检查
        if let fileSize = try? FileManager.default.attributesOfItem(atPath: url.path)[.size] as? NSNumber {
            let maxSize: Int64 = 2 * 1024 * 1024 * 1024 // 2GB
            if fileSize.int64Value > maxSize {
                let alert = NSAlert()
                alert.messageText = "文件过大"
                alert.informativeText = "请选择小于2GB的视频文件进行硬字幕提取。"
                alert.addButton(withTitle: "确定")
                alert.runModal()
                return
            }
        }

        isExtractingSubtitles = true
        subtitleExtractionProgress = 0

        do {
            // 🔑 关键优化：使用与视频检测相同的高效方法
            let segments = try await videoProcessor.processSubtitleRegionFast(
                url: url,
                region: videoProcessor.subtitleRegion.rect,
                recognitionLevel: recognitionLevel,
                progressHandler: { progress in
                    // 在主线程更新进度
                    DispatchQueue.main.async {
                        subtitleExtractionProgress = progress
                    }
                }
            )

            // 处理完成后，将结果转换为SRT格式并导出
            await MainActor.run {
                let srtContent = videoProcessor.exportSubtitlesToSRT()
                self.srtContent = srtContent

                // 🔧 为提取界面创建独立的数据副本
                self.extractOptimizedSegments = segments
                extractionDataManager.setSegments(segments)

                // 自动跳转到视频文本检测界面
                selectedSegment = 0
                isExtractingSubtitles = false

                // 🔧 保存提取界面的字幕缓存
                if let mediaURL = selectedVideo {
                    SubtitleCacheManager.shared.cacheSubtitles(
                        segments,
                        for: mediaURL,
                        interface: .extraction
                    )
                }

                // 显示导出对话框
                exportExtractedSubtitles()
            }
        } catch {
            // print("提取字幕错误: \(error)")
            await MainActor.run {
                isExtractingSubtitles = false
            }
        }
    }

    // 导出提取的字幕
    func exportExtractedSubtitles() {
        let savePanel = NSSavePanel()
        savePanel.allowedContentTypes = [UTType(filenameExtension: "srt")!]
        savePanel.nameFieldStringValue = "extracted_subtitles.srt"
        
        savePanel.begin { result in
            if result == .OK, let url = savePanel.url {
                do {
                    try self.srtContent.write(to: url, atomically: true, encoding: .utf8)
                    // print("字幕已保存到: \(url.path)")
                } catch {
                    print("保存字幕文件失败: \(error)")
                }
            }
        }
    }

    // 添加字幕区域选择覆盖层
    struct SubtitleRegionSelectionOverlay: View {
        @Binding var isSelecting: Bool
        @Binding var selectedRegion: CGRect
        let imageSize: CGSize
        
        @State private var startPoint: CGPoint?
        @State private var currentPoint: CGPoint?
        @State private var finalRect: CGRect? // 新增：保存最终选择的矩形
        
        var body: some View {
            GeometryReader { geometry in
                ZStack {
                    Color.black.opacity(0.5)
                        .edgesIgnoringSafeArea(.all)
                    
                    // 绘制选择区域
                    if let start = startPoint, let current = currentPoint {
                        let rect = calculateRect(from: start, to: current, in: geometry.size)
                        Rectangle()
                            .stroke(Color.yellow, lineWidth: 2)
                            .background(Color.yellow.opacity(0.2))
                            .frame(width: rect.width, height: rect.height)
                            .position(x: rect.midX, y: rect.midY)
                    } else if let finalRect = finalRect {
                        // 显示最终确定的选择框
                        Rectangle()
                            .stroke(Color.green, lineWidth: 3) // 使用绿色边框表示已选择
                            .background(Color.green.opacity(0.2))
                            .frame(width: finalRect.width, height: finalRect.height)
                            .position(x: finalRect.midX, y: finalRect.midY)
                    }
                    
                    // 说明文字
                    VStack {
                        Text(finalRect == nil ? "请拖动鼠标选择字幕区域" : "已选择区域，点击确认按钮完成")
                            .foregroundColor(.white)
                            .padding()
                            .background(Color.black.opacity(0.7))
                            .cornerRadius(8)
                        
                        Spacer()
                    }
                    .padding(.top, 20)
                }
                .contentShape(Rectangle())
                .gesture(
                    DragGesture(minimumDistance: 0)
                        .onChanged { value in
                            if startPoint == nil {
                                startPoint = value.startLocation
                            }
                            currentPoint = value.location
                        }
                        .onEnded { value in
                            if let start = startPoint, let current = currentPoint {
                                let rect = calculateRect(from: start, to: current, in: geometry.size)
                                // 转换为标准化坐标（0-1范围）
                                selectedRegion = normalizeRect(rect, in: geometry.size)
                                // 保存最终矩形用于显示，但不重置拖拽状态
                                finalRect = rect
                                // 重置拖拽状态
                                startPoint = nil
                                currentPoint = nil
                            }
                        }
                )
            }
        }
        
        // 计算矩形区域
        private func calculateRect(from start: CGPoint, to end: CGPoint, in size: CGSize) -> CGRect {
            let minX = min(start.x, end.x)
            let minY = min(start.y, end.y)
            let width = abs(end.x - start.x)
            let height = abs(end.y - start.y)
            
            return CGRect(x: minX, y: minY, width: width, height: height)
        }
        
        // 将像素坐标转换为标准化坐标（0-1范围）
        private func normalizeRect(_ rect: CGRect, in size: CGSize) -> CGRect {
            return CGRect(
                x: rect.origin.x / size.width,
                y: rect.origin.y / size.height,
                width: rect.width / size.width,
                height: rect.height / size.height
            )
        }
    }

    // 添加模型选择器视图
    // var modelSelectorView: some View {
    //     VStack {
    //         Picker("转录模型", selection: $selectedModel) {
    //             Group {
    //                 Text("tiny.en").tag("tiny.en")
    //                 Text("tiny").tag("tiny")
    //                 Text("small.en").tag("small.en")
    //                 Text("small").tag("small")
                    
    //                 // 专业模型添加禁用逻辑
    //                 Text("large-v2(949MB) 🔒").tag("large-v2_949MB")
    //                     .disabled(!LicenseManager.shared.isPro()) // 替换为 isPro
    //                 Text("large-v2_turbo(955MB) 🔒").tag("large-v2_turbo_955MB")
    //                     .disabled(!LicenseManager.shared.isPro()) // 替换为 isPro
    //                 Text("large-v3 🔒").tag("large-v3")
    //                     .disabled(!LicenseManager.shared.isPro()) // 替换为 isPro
    //                 Text("large-v3_turbo 🔒").tag("large-v3_turbo")
    //                     .disabled(!LicenseManager.shared.isPro()) // 替换为 isPro
    //                 Text("large-v3-v20240930 🔒").tag("large-v3-v20240930")
    //                     .disabled(!LicenseManager.shared.isPro()) // 替换为 isPro
    //                 Text("large-v3-v20240930_turbo 🔒").tag("large-v3-v20240930_turbo")
    //                     .disabled(!LicenseManager.shared.isPro()) // 替换为 isPro
    //             }
    //         }
    //         .pickerStyle(MenuPickerStyle())
    //         .onChange(of: selectedModel) { newModel in
    //             // 如果选择了专业模型但没有许可证，显示提示并回退到免费模型
    //             if isProModel(newModel) && !LicenseManager.shared.isPro() { // 替换为 isPro
    //                 showProFeatureAlert() // 不再传递参数
    //                 // 回退到默认免费模型
    //                 DispatchQueue.main.async {
    //                     selectedModel = "small"
    //                 }
    //             }
    //         }
    //     }
    // }



    // 在段落文本编辑完成后触发缓存
    func onSegmentEdit() {
        scheduleCacheUpdate()
    }

    // 在翻译文本编辑完成后触发缓存
    func onTranslationEdit() {
        scheduleCacheUpdate()
    }

    // 片段重新转录功能
    func reTranscribeSegment(_ segment: EditableSegment) {
        let start = segment.startTime
        let end = segment.endTime
        guard let url = selectedVideo else { return }
        let asset = AVAsset(url: url)
        let preset = mediaType == .video ? AVAssetExportPresetAppleM4A : AVAssetExportPresetAppleM4A
        guard let exportSession = AVAssetExportSession(asset: asset, presetName: preset) else { return }
        let tempURL = FileManager.default.temporaryDirectory.appendingPathComponent("reclip_\(UUID().uuidString).m4a")
        exportSession.outputURL = tempURL
        exportSession.outputFileType = .m4a
        exportSession.timeRange = CMTimeRange(start: CMTime(seconds: start, preferredTimescale: 600), duration: CMTime(seconds: end - start, preferredTimescale: 600))
        exportSession.exportAsynchronously {
            DispatchQueue.main.async {
                if exportSession.status == .completed {
                    self.transcribeAudioForURL(url: tempURL, offset: start) { newSegments in
                        // 替换原有 segment
                        // if let idx = self.editableSegments.firstIndex(where: { $0.id == segment.id }) {
                        //     self.editableSegments.remove(at: idx)
                        //     self.editableSegments.insert(contentsOf: newSegments, at: idx)
                        // }
                        if let idx = self.optimizedSegments.firstIndex(where: { $0.id == segment.id }) {
                            self.optimizedSegments.remove(at: idx)
                            self.optimizedSegments.insert(contentsOf: newSegments, at: idx)
                        }
                        // 清理临时文件
                        try? FileManager.default.removeItem(at: tempURL)
                    }
                } else {
                    print("片段导出失败: \(exportSession.error?.localizedDescription ?? "未知错误")")
                }
            }
        }
    }

    // 针对片段的异步转录（不影响全局 editableSegments）
    func transcribeAudioForURL(url: URL, offset: Double, completion: @escaping ([EditableSegment]) -> Void) {
        Task {
            do {
                let whisperKit = try await ensureWhisperModel(modelName: selectedModel)
                let options = DecodingOptions(
                    verbose: true,
                    task: .transcribe,
                    language: transcriptionLanguage,
                    skipSpecialTokens: true,
                    withoutTimestamps: false,
                    wordTimestamps: true
                )
                let results = try await whisperKit.transcribe(audioPath: url.path, decodeOptions: options)
                guard let result = results.first else { completion([]); return }
                // 写入临时 JSON
                let saveResult = writeJSONFile(result: result)
                switch saveResult {
                case .success(let jsonURL):
                    let jsonData = try Data(contentsOf: jsonURL)
                    let transcription = try JSONDecoder().decode(WhisperTranscription.self, from: jsonData)
                    let isCJKLanguage = ["ja", "zh", "zh-Hans", "zh-Hant", "ko","th"].contains(transcriptionLanguage)

                    // 🔑 简单判断：有词级时间戳就用词级，没有就用段落级
                    let segments: [EditableSegment] = transcription.segments.map { seg in
                        if !seg.words.isEmpty {
                            // 有词级时间戳：使用词级处理
                            let words = seg.words.map { w in
                                EditableWord(word: cleanText(w.word), start: w.start + offset, end: w.end + offset, probability: w.probability)
                            }
                            return EditableSegment(words: words, isCJKLanguage: isCJKLanguage)
                        } else {
                            // 没有词级时间戳：使用段落级处理
                            let word = EditableWord(word: cleanText(seg.text), start: seg.start + offset, end: seg.end + offset, probability: 1.0)
                            return EditableSegment(words: [word], isCJKLanguage: isCJKLanguage)
                        }
                    }
                    completion(segments)
                    try? FileManager.default.removeItem(at: jsonURL)
                case .failure(let error):
                    print("写入JSON失败: \(error.localizedDescription)")
                    completion([])
                }
            } catch {
                print("片段转录失败: \(error.localizedDescription)")
                completion([])
            }
        }
    }
}

struct EditableWord: Identifiable {
    var id = UUID()
    let word: String
    let start: Double
    let end: Double
    let probability: Double
}

struct CorrectionSuggestion: Codable, Hashable, Identifiable {
    let id = UUID()
    let originalText: String
    let correctedText: String
    let category: String

    enum CodingKeys: String, CodingKey {
        case originalText = "original_text"
        case correctedText = "corrected_text"
        case category
    }
}

struct EditableSegment: Identifiable {
    var id: UUID
    var words: [EditableWord]
    let isCJKLanguage: Bool  // 添加标识是否为中文文本
    var correctionSuggestions: [CorrectionSuggestion]?
    
    var text: String {
        if isCJKLanguage {
            // 中文不需要空格分隔
            return words.map { $0.word }.joined()
        } else {
            // 其他语言需要空格分隔
            return words.map { $0.word }.joined(separator: " ")
        }
    }
    
    var startTime: Double {
        words.first?.start ?? 0
    }
    
    var endTime: Double {
        words.last?.end ?? 0
    }

    var translatedText: String? // 新增属性用存储翻译结果
    
    // 添加初始化方法
    init(id: UUID = UUID(), words: [EditableWord], isCJKLanguage: Bool = false) {
        self.id = id
        self.words = words
        self.isCJKLanguage = isCJKLanguage
    }
    
    // 添加更新文本的方法
    mutating func updateText(_ newText: String) {
        if isCJKLanguage {
            // 中文文本作为整体存储在一个 EditableWord 中
            // 对于中文、日语等语言，不进行分词处理，保持整段文本
            words = [EditableWord(
                word: newText,
                start: words.first?.start ?? 0,
                end: words.last?.end ?? 0,
                probability: 1.0
            )]
        } else {
            // 其他语言按空格分割
            let newWords = newText.split(separator: " ").map { word -> EditableWord in
                EditableWord(
                    word: String(word),
                    start: words.first?.start ?? 0,
                    end: words.last?.end ?? 0,
                    probability: 1.0
                )
            }
            words = newWords
        }
    }
}

struct SegmentView: View {  
    let segment: EditableSegment
    let isSelected: Bool
    let isLastSegment: Bool
    @Binding var cursorPosition: Int?
    let onSelect: () -> Void
    let onSplit: (Int) -> Void
    let onMerge: () -> Void
    let onTimeClick: (Double) -> Void
    let onUpdateText: (String) -> Void
    let onUpdateTranslation: (String) -> Void
    let onEdit: () -> Void // 新增编辑回调
    let onAcceptSuggestion: (CorrectionSuggestion) -> Void // 新增 接受
    let onIgnoreSuggestion: (CorrectionSuggestion) -> Void // 新增“忽略”回调
    let onReTranscribe: () -> Void // 新增回调
    let enableReTranscribe: Bool // 🔧 控制重新转录功能是否可用

    @State private var isEditingOriginal: Bool = false
    @State private var isEditingTranslation: Bool = false
    @FocusState private var isTextEditorFocused: Bool // 添加焦点状态
    @Environment(\.scenePhase) private var scenePhase // 用于检测点击外部
    @State private var localText: String = "" // 原文本地文本状态
    @State private var localTranslationText: String = "" // 添加翻译文本的本地状态
    
    var body: some View {
        HStack(alignment: .top, spacing: 2) {
            // 左侧内容
            VStack(alignment: .leading, spacing: 6) {
                // 合并按钮
                if !isLastSegment { // 使用 isLastSegment
                    Button("合并") {
                        onMerge()  // 调用合并回调
                    }
                    .frame(width: 50) // 设置按钮宽度
                    .buttonStyle(BorderlessButtonStyle())
                    .foregroundColor(.white) // 设置按钮文本颜色白色
                    .background(Color.blue.opacity(0.3)) // 设置按钮背景颜色
                    .cornerRadius(5) // 设置圆角  
                }
                
                // 编辑按钮
                Button(isEditingOriginal ? "完成" : "编辑") {
                    if !isEditingOriginal {
                        // 进入编辑模式时，初始化本地文本
                        localText = segment.text
                    }
                    isEditingOriginal.toggle()
                }
                .frame(width: 50)
                .buttonStyle(BorderlessButtonStyle())
                .foregroundColor(.white)
                .background(Color.green.opacity(0.3))
                .cornerRadius(5)
            }
            .padding(.top, 50)
            .frame(width: 80)
            
            // 右侧内容
            VStack(alignment: .leading, spacing: 2) {
                // 可点击的时间戳
                // 时间戳和重新转录按钮并排
                HStack {
                    Button(action: {
                        onTimeClick(segment.startTime)
                    }) {
                        Text("\(TimeFormatter.formatSRT(seconds: segment.startTime)) --> \(TimeFormatter.formatSRT(seconds: segment.endTime))")
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .padding(8)
                            .background(Color(NSColor.controlBackgroundColor).opacity(0.5))
                            .cornerRadius(2)
                    }
                    .buttonStyle(PlainButtonStyle())

                    Button(action: {
                        onReTranscribe()
                    }) {
                        Image(systemName: "arrow.clockwise.circle")
                            .foregroundColor(enableReTranscribe ? .primary : .secondary) // 🔧 添加禁用状态颜色
                            .help(enableReTranscribe ? "重新转录本片段" : "重新转录（仅AI优化界面可用）") // 🔧 添加提示信息
                    }
                    .disabled(!enableReTranscribe) // 🔧 添加禁用控制
                }
                
                // 原文编辑区域
                if isEditingOriginal {
                    TextEditor(text: $localText)
                        .frame(height: 60)
                        .padding(4)
                        .background(Color(NSColor.textBackgroundColor)) // 设置文本框背景颜色
                        .cornerRadius(4)
                        .onChange(of: localText) { newValue in
                            onUpdateText(newValue)
                            onEdit()
                        }
                } else {
                    // 可点击的段落区域
                    HStack(spacing: 4) {
                        ForEach(segment.words, id: \.id) { word in
                            Text(word.word)
                                .padding(.horizontal, 4)
                                .padding(.vertical, 1)
                                .background(Color.blue.opacity(0.1))
                                .cornerRadius(4)
                                .onTapGesture {
                                    onSelect()
                                    let index = segment.words.firstIndex(where: { $0.id == word.id })!
                                    onSplit(index)
                                }
                        }
                    }
                    .padding(8)
                    .background(isSelected ? Color.green.opacity(0.1) : Color.clear) //合并操作后显示绿色背景
                    .cornerRadius(4)
                }

                // ✅ 核心UI逻辑：如果存在校对建议，就显示它们
                if let suggestions = segment.correctionSuggestions, !suggestions.isEmpty {
                    // 使用 ForEach 遍历并显示每一条建议
                    ForEach(suggestions) { suggestion in
                        HStack(spacing: 5) {
                            Image(systemName: "sparkle") // 魔法棒图标
                                .foregroundColor(.orange)
                            Text("AI建议:")
                                .font(.caption)
                                .foregroundColor(.secondary)
                            Text("\"\(suggestion.originalText)\"")
                                .strikethrough(color: .red)
                            Image(systemName: "arrow.right")
                            Text("\"\(suggestion.correctedText)\"")
                                .foregroundColor(.green)
                            
                            Spacer()
                            
                            Button {
                                // 点击时，调用回调函数
                                onAcceptSuggestion(suggestion)
                            } label: {
                                Image(systemName: "checkmark.circle.fill")
                                Text("接受")
                            }
                            .buttonStyle(.borderless)
                            .foregroundColor(.accentColor)
                            .controlSize(.small)

                            // ✅ 新增“忽略”按钮
                            Button {
                                onIgnoreSuggestion(suggestion)
                            } label: {
                                Image(systemName: "xmark.circle")
                                Text("忽略")
                            }
                            .buttonStyle(.borderless)
                            .foregroundColor(.gray)
                            .controlSize(.small)
                        }
                        .font(.caption)
                        .padding(6)
                        .background(Color.secondary.opacity(0.1))
                        .cornerRadius(6)
                        .transition(.asymmetric(insertion: .scale, removal: .opacity))
                    }
                }
                
                // 翻译文本编辑区域
                if let translatedText = segment.translatedText {
                    if isEditingTranslation {
                        TextEditor(text: $localTranslationText)
                            .focused($isTextEditorFocused) // 添加焦点绑定
                            .frame(height: 60)
                            .foregroundColor(.gray)
                            .padding(4)
                            .background(Color(NSColor.textBackgroundColor))// 设置背景颜色
                            .cornerRadius(4)
                            .onChange(of: localTranslationText) { newValue in
                                onUpdateTranslation(newValue)
                                onEdit() // 调用编辑回调
                            }
                            .onChange(of: isTextEditorFocused) { isFocused in
                                // 当失去焦点时关闭编辑模式
                                if !isFocused {
                                    isEditingTranslation = false
                                }
                            }
                    } else {
                        Text(translatedText)
                            .foregroundColor(.gray)
                            .padding(8)
                            .background(
                                RoundedRectangle(cornerRadius: 4)
                                    .fill(Color.clear)
                                    .contentShape(Rectangle())
                            )
                            .onTapGesture {
                                localTranslationText = translatedText // 初始化本地翻译文本
                                isEditingTranslation = true
                                isTextEditorFocused = true // 自动获取焦点
                            }
                    }
                }
            }
        }
        .padding(.vertical, 4)
        .padding(.horizontal, 10)
        .background(segment.correctionSuggestions != nil && segment.correctionSuggestions?.isEmpty == false ? Color.yellow.opacity(0.2) : Color.clear)
        .animation(.default, value: segment.correctionSuggestions) // 当建议被移除时，添加动画效果
        // 添加点击外部区域关闭编辑框的功能
        .onTapGesture {
            if isEditingTranslation {
                isEditingTranslation = false
                isTextEditorFocused = false
            }
        }
    }
}

struct EditHistory {
    private(set) var undoStack: [State] = []
    private(set) var redoStack: [State] = []
    
    struct State {
        let segments: [EditableSegment]
        let selectedId: UUID?
    }
    
    mutating func push(_ state: State) {
        undoStack.append(state)
        redoStack.removeAll() // 清除做栈
    }
    
    mutating func undo() -> State? {
        guard let current = undoStack.popLast() else { return nil }
        redoStack.append(current)
        return undoStack.last
    }
    
    mutating func redo() -> State? {
        guard let next = redoStack.popLast() else { return nil }
        undoStack.append(next)
        return next
    }
}

struct SegmentDropDelegate: DropDelegate {
    let item: EditableSegment
    @Binding var items: [EditableSegment]
    @Binding var draggedItem: UUID?
    
    func performDrop(info: DropInfo) -> Bool {
        guard let draggedItem = draggedItem else { return false }
        let fromIndex = items.firstIndex { $0.id == draggedItem }
        let toIndex = items.firstIndex { $0.id == item.id }
        
        if let fromIndex = fromIndex, let toIndex = toIndex {
            withAnimation {
                let item = items.remove(at: fromIndex)
                items.insert(item, at: toIndex)
            }
        }
        
        return true
    }
    
    func dropUpdated(info: DropInfo) -> DropProposal? {
        return DropProposal(operation: .move)
    }
}

// 添加一个时间格式化工具类
struct TimeFormatter {
    static func formatSRT(seconds: Double) -> String {
        let hours = Int(seconds) / 3600
        let minutes = (Int(seconds) % 3600) / 60
        let secs = Int(seconds) % 60
        let milliseconds = Int((seconds - Double(Int(seconds))) * 1000)
        
        return String(format: "%02d:%02d:%02d,%03d", hours, minutes, secs, milliseconds)
    }
    
    static func formatSimple(seconds: Double) -> String {
        let minutes = Int(seconds) / 60
        let secs = Int(seconds) % 60
        return String(format: "%02d:%02d", minutes, secs)
    }
}

// 确保 EditableSegment 和相关类型可以被编码和解码
extension EditableSegment: Codable {
    enum CodingKeys: String, CodingKey {
        case id
        case words
        case translatedText
        case isCJKLanguage
    }
    
    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        id = try container.decode(UUID.self, forKey: .id)
        words = try container.decode([EditableWord].self, forKey: .words)
        translatedText = try container.decodeIfPresent(String.self, forKey: .translatedText)
        isCJKLanguage = try container.decode(Bool.self, forKey: .isCJKLanguage)
    }
    
    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(id, forKey: .id)
        try container.encode(words, forKey: .words)
        try container.encode(translatedText, forKey: .translatedText)
        try container.encode(isCJKLanguage, forKey: .isCJKLanguage)
    }
}

extension EditableWord: Codable {
    enum CodingKeys: String, CodingKey {
        case id
        case word
        case start
        case end
        case probability
    }
    
    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        id = try container.decode(UUID.self, forKey: .id)
        word = try container.decode(String.self, forKey: .word)
        start = try container.decode(Double.self, forKey: .start)
        end = try container.decode(Double.self, forKey: .end)
        probability = try container.decode(Double.self, forKey: .probability)
    }
    
    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(id, forKey: .id)
        try container.encode(word, forKey: .word)
        try container.encode(start, forKey: .start)
        try container.encode(end, forKey: .end)
        try container.encode(probability, forKey: .probability)
    }
}

// 添加以下辅助视图
struct TextBoundingBoxesView: View {
    let textSegments: [TextSegment]
    let currentTime: Double
    
    var body: some View {
        GeometryReader { geometry in
            ForEach(textSegments) { segment in
                if isSegmentVisible(segment) {
                    let scaledRect = scaleRect(segment.textBounds, to: geometry.size)
                    Rectangle()
                        .stroke(Color.red, lineWidth: 2)
                        .frame(
                            width: scaledRect.width,
                            height: scaledRect.height
                        )
                        .position(
                            x: scaledRect.midX,
                            y: geometry.size.height - scaledRect.midY // 关键修改：翻转 Y 坐标
                        )
                        .overlay(
                            Text(segment.text)
                                .font(.caption2)
                                .foregroundColor(.red)
                                .padding(2)
                                .background(Color.black.opacity(0.5))
                                .cornerRadius(2)
                                .position(
                                    x: scaledRect.midX,
                                    y: geometry.size.height - scaledRect.maxY - 10 // 调整文本位置
                                )
                        )
                }
            }
        }
    }
    
    private func isSegmentVisible(_ segment: TextSegment) -> Bool {
        return currentTime >= segment.startTime && currentTime <= segment.endTime
    }
    
    private func scaleRect(_ rect: CGRect, to size: CGSize) -> CGRect {
        // Vision 框架返回的坐标是从左下角开始的，需要转换为左上角开始
        let normalizedRect = CGRect(
            x: rect.origin.x,
            y: rect.origin.y,
            width: rect.width,
            height: rect.height
        )
        
        // 缩放到实际尺寸
        return CGRect(
            x: normalizedRect.origin.x * size.width,
            y: normalizedRect.origin.y * size.height,
            width: normalizedRect.width * size.width,
            height: normalizedRect.height * size.height
        )
    }
}

struct TextSegmentView: View {
    let segment: TextSegment
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            VStack(alignment: .leading, spacing: 4) {
                Text(segment.text)
                    .lineLimit(2)
                
                HStack {
                    Text(String(format: "%.2f", segment.startTime))
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Text("-")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Text(String(format: "%.2f", segment.endTime))
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Spacer()
                    
                    // 显示置信度
                    Text(String(format: "置信度: %.1f%%", segment.confidence * 100))
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            .padding(8)
            .background(Color.secondary.opacity(0.1))
            .cornerRadius(8)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// 在 ContentView 外部添加 VideoPlayerView 定义
struct VideoPlayerView: NSViewRepresentable {
    let player: AVPlayer
    
    func makeNSView(context: Context) -> AVPlayerView {
        let view = AVPlayerView()
        view.player = player
        view.controlsStyle = .floating
        view.showsFullScreenToggleButton = true
        return view
    }
    
    func updateNSView(_ nsView: AVPlayerView, context: Context) {
        nsView.player = player
    }
}

// 添加新的卡片视图组件
struct TextSegmentCard: View {
    let segment: TextSegment
    let highlightedText: AttributedString // 新增高亮文本参数
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            VStack(alignment: .leading, spacing: 4) {
                // 缩略图（如果有）
                if let frame = segment.frames.first {
                    Image(nsImage: frame.image)
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                        .frame(height: 100)
                }

                // 显示高亮文本
                Text(highlightedText)
                    .lineLimit(2)
                    .font(.caption)
                
                // 时间和置信度
                HStack {
                    Text(String(format: "%.2f", segment.startTime))
                        .font(.caption2)
                        .foregroundColor(.secondary)
                    
                    Spacer()
                    
                    Text(String(format: "%.0f%%", segment.confidence * 100))
                        .font(.caption2)
                        .foregroundColor(.secondary)
                }
            }
            .padding(8)
            .frame(maxWidth: .infinity)
            .background(Color.secondary.opacity(0.1))
            .cornerRadius(8)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - 转录设置对话框视图
struct TranscriptionSettingsView: View {
    @Binding var transcriptionLanguage: String
    @Binding var selectedModel: String
    @Binding var enableVAD: Bool

    let onConfirm: () -> Void
    let onCancel: () -> Void

    var body: some View {
        VStack(spacing: 20) {
            // 标题
            VStack(alignment: .leading, spacing: 4) {
                HStack {
                    Text("转录设置")
                        .font(.title2)
                        .fontWeight(.semibold)
                    Spacer()
                }
            }

            // 设置选项
            VStack(alignment: .leading, spacing: 16) {
                // 转录语言选择
                VStack(alignment: .leading, spacing: 8) {
                    Text("转录语言")
                        .font(.headline)

                    Picker("转录语言", selection: $transcriptionLanguage) {
                        Text("英语").tag("en")
                        Text("中文").tag("zh")
                        Text("日语").tag("ja")
                        Text("韩语").tag("ko")
                        Text("法语").tag("fr")
                        Text("德语").tag("de")
                        Text("西班牙语").tag("es")
                        Text("意大利语").tag("it")
                        Text("泰语").tag("th")
                    }
                    .pickerStyle(MenuPickerStyle())
                    .onChange(of: transcriptionLanguage) { newLanguage in
                        if newLanguage != "en" && selectedModel == "small.en" {
                            // 如果选择了中文、日语、韩语但模型是 small.en，自动切换到 small
                            selectedModel = "small"
                        }
                    }
                    .frame(maxWidth: .infinity, alignment: .leading)
                }

                // 转录模型选择
                VStack {
                    Picker("转录模型", selection: $selectedModel) {
                        Group {
                            Text("tiny.en").tag("tiny.en")
                            Text("tiny").tag("tiny")
                            Text("small.en").tag("small.en")
                            Text("small").tag("small")
                            
                            // 专业模型添加禁用逻辑
                            Text("large-v2(949MB) 🔒").tag("large-v2_949MB")
                                .disabled(!LicenseManager.shared.isPro()) // 替换为 isPro
                            Text("large-v2_turbo(955MB) 🔒").tag("large-v2_turbo_955MB")
                                .disabled(!LicenseManager.shared.isPro()) // 替换为 isPro
                            Text("large-v3 🔒").tag("large-v3")
                                .disabled(!LicenseManager.shared.isPro()) // 替换为 isPro
                            Text("large-v3_turbo 🔒").tag("large-v3_turbo")
                                .disabled(!LicenseManager.shared.isPro()) // 替换为 isPro
                            Text("large-v3-v20240930 🔒").tag("large-v3-v20240930")
                                .disabled(!LicenseManager.shared.isPro()) // 替换为 isPro
                            Text("large-v3-v20240930_turbo 🔒").tag("large-v3-v20240930_turbo")
                                .disabled(!LicenseManager.shared.isPro()) // 替换为 isPro
                        }
                    }
                    .pickerStyle(MenuPickerStyle())
                    .onChange(of: selectedModel) { newModel in
                        // 🔑 如果选择了专业模型但没有许可证，显示提示并回退到免费模型
                        if ProFeatureHelper.shared.isProModel(newModel) && !LicenseManager.shared.isPro() {
                            ProFeatureHelper.shared.showProFeatureAlert(closeTranscriptionSettings: onCancel)
                            // 回退到默认免费模型
                            DispatchQueue.main.async {
                                selectedModel = "small"
                            }
                        }
                    }
                }

                // VAD智能分块设置
                VStack(alignment: .leading, spacing: 8) {
                    Text("高级选项")
                        .font(.headline)

                    HStack {
                        Toggle("启用VAD智能分块", isOn: $enableVAD)
                            .toggleStyle(SwitchToggleStyle())

                        Spacer()
                    }

                    Text("音频分段策略：选择 VAD 可在静音处智能分割音频，可提升转录准确率")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            .padding(.vertical, 8)

            Spacer()

            // 操作按钮
            HStack(spacing: 12) {
                Button("取消") {
                    onCancel()
                }
                .buttonStyle(.bordered)
                .keyboardShortcut(.escape)
                Spacer()

                Button("开始转录") {
                    // 🔑 @AppStorage会自动保存设置，无需手动保存
                    onConfirm()
                }
                .buttonStyle(.borderedProminent)
                .keyboardShortcut(.return)
            }
        }
        .padding(24)
        .frame(width: 400, height: 350)
    }
}
